/**
 * Main Application Styles
 * =======================
 * 
 * Component-specific styles for the App component.
 * Includes loading states, error handling, and layout styles.
 */

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Status Bar (Development) */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #1e293b;
  color: #f8fafc;
  font-size: 0.75rem;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  border-bottom: 1px solid #334155;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-indicator.connected {
  color: #22c55e;
}

.status-indicator.disconnected {
  color: #ef4444;
}

.last-updated,
.version {
  color: #94a3b8;
}

/* Loading States */
.app-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.loading-content {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.loading-subtitle {
  font-size: 1rem;
  opacity: 0.8;
  margin-bottom: 2rem;
}

.loading-progress {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.loading-bar {
  height: 100%;
  background-color: white;
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progress {
  0% {
    width: 0%;
    margin-left: 0%;
  }
  50% {
    width: 75%;
    margin-left: 25%;
  }
  100% {
    width: 0%;
    margin-left: 100%;
  }
}

/* Error States */
.app-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #fef2f2;
  padding: 1rem;
}

.error-content {
  background-color: white;
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 1rem;
}

.error-message {
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.error-actions .btn {
  min-width: 120px;
}

.error-details {
  text-align: left;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.error-details summary {
  font-weight: 500;
  cursor: pointer;
  color: #6b7280;
  margin-bottom: 1rem;
}

.error-details summary:hover {
  color: #374151;
}

.error-stack {
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  white-space: pre-wrap;
  overflow-x: auto;
  border: 1px solid #e5e7eb;
}

/* Navigation Styles */
.app-nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 40;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-tab {
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  font-size: 1rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.nav-tab:hover {
  color: #374151;
  background-color: #f9fafb;
}

.nav-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background-color: #eff6ff;
}

.nav-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  background-color: #f3f4f6;
}

.status-indicator.connected {
  color: #059669;
  background-color: #d1fae5;
}

.status-indicator.disconnected {
  color: #dc2626;
  background-color: #fee2e2;
}

/* Footer */
.app-footer {
  background-color: white;
  border-top: 1px solid #e5e7eb;
  padding: 1rem 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-text {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-link {
  color: #6b7280;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.15s ease-in-out;
}

.footer-link:hover {
  color: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-loading,
  .app-error {
    padding: 1rem;
  }
  
  .loading-content,
  .error-content {
    padding: 2rem 1.5rem;
  }
  
  .loading-title,
  .error-title {
    font-size: 1.25rem;
  }
  
  .loading-subtitle,
  .error-message {
    font-size: 0.875rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .btn {
    width: 100%;
    max-width: 200px;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-links {
    justify-content: center;
  }
  
  .status-bar {
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .loading-spinner {
    width: 40px;
    height: 40px;
    border-width: 3px;
  }
  
  .loading-title,
  .error-title {
    font-size: 1.125rem;
  }
  
  .error-icon {
    font-size: 3rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .app-loading {
    background: #000;
    color: #fff;
  }
  
  .loading-spinner {
    border-color: #fff;
    border-top-color: #000;
  }
  
  .error-content {
    border: 2px solid #000;
  }
  
  .status-bar {
    background-color: #000;
    border-bottom-color: #fff;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner,
  .loading-bar {
    animation: none;
  }
  
  .footer-link {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .app-loading,
  .app-error,
  .status-bar,
  .app-footer {
    display: none !important;
  }
  
  .app {
    background: white !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .app {
    background-color: #0f172a;
  }
  
  .app-footer {
    background-color: #1e293b;
    border-top-color: #334155;
  }
  
  .footer-text,
  .footer-link {
    color: #94a3b8;
  }
  
  .footer-link:hover {
    color: #60a5fa;
  }
  
  .error-content {
    background-color: #1e293b;
    color: #f8fafc;
  }
  
  .error-title {
    color: #f87171;
  }
  
  .error-message {
    color: #94a3b8;
  }
  
  .error-stack {
    background-color: #334155;
    color: #cbd5e1;
    border-color: #475569;
  }
  
  .error-details {
    border-top-color: #475569;
  }
  
  .error-details summary {
    color: #94a3b8;
  }
  
  .error-details summary:hover {
    color: #f8fafc;
  }
}