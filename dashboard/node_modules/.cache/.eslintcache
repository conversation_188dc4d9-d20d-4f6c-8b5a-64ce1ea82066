[{"/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/index.js": "1", "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/reportWebVitals.js": "2", "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/App.js": "3", "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/components/MomentumDashboard.js": "4", "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/components/AdvancedAnalytics.js": "5", "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/hooks/useWebSocket.js": "6"}, {"size": 4067, "mtime": 1752011732701, "results": "7", "hashOfConfig": "8"}, {"size": 788, "mtime": 1752011798138, "results": "9", "hashOfConfig": "8"}, {"size": 9021, "mtime": 1752014731804, "results": "10", "hashOfConfig": "8"}, {"size": 27009, "mtime": 1752014190774, "results": "11", "hashOfConfig": "8"}, {"size": 15390, "mtime": 1752014671493, "results": "12", "hashOfConfig": "8"}, {"size": 5899, "mtime": 1752014495453, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ls2hdr", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/index.js", [], [], "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/reportWebVitals.js", [], [], "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/App.js", ["32", "33", "34"], [], "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/components/MomentumDashboard.js", ["35", "36", "37", "38", "39", "40", "41"], [], "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/components/AdvancedAnalytics.js", ["42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52"], [], "/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/hooks/useWebSocket.js", ["53"], [], {"ruleId": "54", "severity": 1, "message": "55", "line": 35, "column": 22, "nodeType": "56", "messageId": "57", "endLine": 35, "endColumn": 29}, {"ruleId": "54", "severity": 1, "message": "58", "line": 36, "column": 5, "nodeType": "56", "messageId": "57", "endLine": 36, "endColumn": 19}, {"ruleId": "59", "severity": 1, "message": "60", "line": 69, "column": 6, "nodeType": "61", "endLine": 69, "endColumn": 8, "suggestions": "62"}, {"ruleId": "54", "severity": 1, "message": "63", "line": 15, "column": 29, "nodeType": "56", "messageId": "57", "endLine": 15, "endColumn": 39}, {"ruleId": "54", "severity": 1, "message": "64", "line": 16, "column": 3, "nodeType": "56", "messageId": "57", "endLine": 16, "endColumn": 11}, {"ruleId": "54", "severity": 1, "message": "65", "line": 16, "column": 24, "nodeType": "56", "messageId": "57", "endLine": 16, "endColumn": 32}, {"ruleId": "54", "severity": 1, "message": "66", "line": 16, "column": 34, "nodeType": "56", "messageId": "57", "endLine": 16, "endColumn": 38}, {"ruleId": "54", "severity": 1, "message": "67", "line": 16, "column": 55, "nodeType": "56", "messageId": "57", "endLine": 16, "endColumn": 66}, {"ruleId": "54", "severity": 1, "message": "68", "line": 48, "column": 13, "nodeType": "56", "messageId": "57", "endLine": 48, "endColumn": 29}, {"ruleId": "59", "severity": 1, "message": "69", "line": 206, "column": 6, "nodeType": "61", "endLine": 206, "endColumn": 38, "suggestions": "70"}, {"ruleId": "54", "severity": 1, "message": "71", "line": 11, "column": 38, "nodeType": "56", "messageId": "57", "endLine": 11, "endColumn": 45}, {"ruleId": "54", "severity": 1, "message": "72", "line": 13, "column": 3, "nodeType": "56", "messageId": "57", "endLine": 13, "endColumn": 12}, {"ruleId": "54", "severity": 1, "message": "73", "line": 13, "column": 20, "nodeType": "56", "messageId": "57", "endLine": 13, "endColumn": 29}, {"ruleId": "54", "severity": 1, "message": "74", "line": 13, "column": 31, "nodeType": "56", "messageId": "57", "endLine": 13, "endColumn": 35}, {"ruleId": "54", "severity": 1, "message": "75", "line": 13, "column": 52, "nodeType": "56", "messageId": "57", "endLine": 13, "endColumn": 63}, {"ruleId": "54", "severity": 1, "message": "76", "line": 15, "column": 24, "nodeType": "56", "messageId": "57", "endLine": 15, "endColumn": 34}, {"ruleId": "54", "severity": 1, "message": "77", "line": 15, "column": 36, "nodeType": "56", "messageId": "57", "endLine": 15, "endColumn": 45}, {"ruleId": "54", "severity": 1, "message": "78", "line": 15, "column": 47, "nodeType": "56", "messageId": "57", "endLine": 15, "endColumn": 61}, {"ruleId": "54", "severity": 1, "message": "79", "line": 15, "column": 63, "nodeType": "56", "messageId": "57", "endLine": 15, "endColumn": 78}, {"ruleId": "54", "severity": 1, "message": "80", "line": 15, "column": 80, "nodeType": "56", "messageId": "57", "endLine": 15, "endColumn": 85}, {"ruleId": "54", "severity": 1, "message": "81", "line": 23, "column": 10, "nodeType": "56", "messageId": "57", "endLine": 23, "endColumn": 15}, {"ruleId": "59", "severity": 1, "message": "82", "line": 110, "column": 6, "nodeType": "61", "endLine": 110, "endColumn": 34, "suggestions": "83"}, "no-unused-vars", "'wsError' is assigned a value but never used.", "Identifier", "unusedVar", "'requestRefresh' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'checkSystemRequirements' and 'setupPerformanceMonitoring'. Either include them or remove the dependency array.", "ArrayExpression", ["84"], "'DollarSign' is defined but never used.", "'Download' is defined but never used.", "'Settings' is defined but never used.", "'Info' is defined but never used.", "'CheckCircle' is defined but never used.", "'benchmarkReturns' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchStrategyData'. Either include it or remove the dependency array.", ["85"], "'useMemo' is defined but never used.", "'LineChart' is defined but never used.", "'AreaChart' is defined but never used.", "'Area' is defined but never used.", "'ScatterPlot' is defined but never used.", "'RadarChart' is defined but never used.", "'PolarGrid' is defined but never used.", "'PolarAngleAxis' is defined but never used.", "'PolarRadiusAxis' is defined but never used.", "'Radar' is defined but never used.", "'error' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'scheduleReconnect'. Either include it or remove the dependency array.", ["86"], {"desc": "87", "fix": "88"}, {"desc": "89", "fix": "90"}, {"desc": "91", "fix": "92"}, "Update the dependencies array to be: [checkSystemRequirements, setupPerformanceMonitoring]", {"range": "93", "text": "94"}, "Update the dependencies array to be: [fetchStrategyData, generateSimulatedData, onError]", {"range": "95", "text": "96"}, "Update the dependencies array to be: [url, options.socketOptions, scheduleReconnect]", {"range": "97", "text": "98"}, [1942, 1944], "[checkSystemRequirements, setupPerformanceMonitoring]", [7382, 7414], "[fetchStrategyData, generateSimulatedData, onError]", [3419, 3447], "[url, options.socketOptions, scheduleReconnect]"]