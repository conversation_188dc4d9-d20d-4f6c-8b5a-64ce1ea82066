{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/App.js\",\n  _s = $RefreshSig$();\n/**\n * Main Application Component\n * =========================\n * \n * Root component for the Momentum Strategy Dashboard.\n * Handles application-level state, routing, and layout.\n */\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport MomentumDashboard from './components/MomentumDashboard';\nimport AdvancedAnalytics from './components/AdvancedAnalytics';\nimport useWebSocket from './hooks/useWebSocket';\nimport './App.css';\n\n// App-level configuration\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst APP_CONFIG = {\n  title: 'Momentum Strategy Dashboard',\n  version: '1.0.0',\n  refreshInterval: 30000,\n  // 30 seconds\n  maxRetries: 3\n};\nfunction App() {\n  _s();\n  // Application state\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [connectionStatus, setConnectionStatus] = useState('connected');\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  // Initialize application\n  useEffect(() => {\n    const initializeApp = async () => {\n      try {\n        // Simulate initialization delay\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Check system requirements\n        checkSystemRequirements();\n\n        // Set up performance monitoring\n        setupPerformanceMonitoring();\n\n        // Mark as loaded\n        setIsLoading(false);\n        setLastUpdated(new Date());\n        console.log('✅ Dashboard initialized successfully');\n      } catch (err) {\n        console.error('❌ Failed to initialize dashboard:', err);\n        setError(err.message);\n        setIsLoading(false);\n      }\n    };\n    initializeApp();\n  }, []);\n\n  // Check browser compatibility and system requirements\n  const checkSystemRequirements = useCallback(() => {\n    const requirements = {\n      localStorage: typeof Storage !== 'undefined',\n      fetch: typeof fetch !== 'undefined',\n      promises: typeof Promise !== 'undefined',\n      es6: typeof Symbol !== 'undefined'\n    };\n    const missingFeatures = Object.entries(requirements).filter(([feature, supported]) => !supported).map(([feature]) => feature);\n    if (missingFeatures.length > 0) {\n      throw new Error(`Unsupported browser. Missing features: ${missingFeatures.join(', ')}`);\n    }\n\n    // Check for minimum screen size\n    if (window.innerWidth < 320) {\n      console.warn('⚠️ Small screen detected. Dashboard may have limited functionality.');\n    }\n  }, []);\n\n  // Set up performance monitoring\n  const setupPerformanceMonitoring = useCallback(() => {\n    // Monitor memory usage\n    if ('memory' in performance) {\n      const checkMemory = () => {\n        const memory = performance.memory;\n        const memoryUsage = {\n          used: Math.round(memory.usedJSHeapSize / 1048576),\n          // MB\n          total: Math.round(memory.totalJSHeapSize / 1048576),\n          // MB\n          limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB\n        };\n\n        // Warn if memory usage is high\n        if (memoryUsage.used / memoryUsage.limit > 0.8) {\n          console.warn('⚠️ High memory usage detected:', memoryUsage);\n        }\n      };\n\n      // Check memory every 5 minutes\n      const memoryInterval = setInterval(checkMemory, 300000);\n\n      // Cleanup on unmount\n      return () => clearInterval(memoryInterval);\n    }\n\n    // Monitor network status\n    const updateOnlineStatus = () => {\n      setConnectionStatus(navigator.onLine ? 'connected' : 'disconnected');\n    };\n    window.addEventListener('online', updateOnlineStatus);\n    window.addEventListener('offline', updateOnlineStatus);\n\n    // Cleanup event listeners\n    return () => {\n      window.removeEventListener('online', updateOnlineStatus);\n      window.removeEventListener('offline', updateOnlineStatus);\n    };\n  }, []);\n\n  // Handle application errors\n  const handleError = useCallback((error, errorInfo) => {\n    console.error('Application Error:', error, errorInfo);\n    setError(error.message);\n\n    // In production, you might want to send this to an error reporting service\n    // errorReportingService.captureException(error, { extra: errorInfo });\n  }, []);\n\n  // Retry functionality for failed operations\n  const handleRetry = useCallback(() => {\n    setError(null);\n    setIsLoading(true);\n    window.location.reload();\n  }, []);\n\n  // Loading screen\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app-loading\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"loading-title\",\n          children: \"Loading Strategy Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"loading-subtitle\",\n          children: \"Initializing quantitative analytics...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-progress\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-bar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error screen\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app-error\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"error-title\",\n          children: \"Dashboard Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: handleRetry,\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: () => window.location.href = 'mailto:<EMAIL>',\n            children: \"Contact Support\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n          className: \"error-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            children: \"Technical Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            className: \"error-stack\",\n            children: [\"User Agent: \", navigator.userAgent, '\\n', \"Timestamp: \", new Date().toISOString(), '\\n', \"Screen: \", window.screen.width, \"x\", window.screen.height, '\\n', \"Viewport: \", window.innerWidth, \"x\", window.innerHeight]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Main application\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"status-bar\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `status-indicator ${connectionStatus}`,\n        children: [connectionStatus === 'connected' ? '🟢' : '🔴', \" \", connectionStatus]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"last-updated\",\n        children: [\"Last updated: \", lastUpdated.toLocaleTimeString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"version\",\n        children: [\"v\", APP_CONFIG.version]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"app-main\",\n      children: /*#__PURE__*/_jsxDEV(MomentumDashboard, {\n        onError: handleError,\n        connectionStatus: connectionStatus,\n        lastUpdated: lastUpdated\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"app-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-text\",\n          children: \"\\xA9 2024 Momentum Strategy Dashboard by Rishabh Ashok Patil. Built with React & Recharts for quantitative finance.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://github.com/yourusername/momentum-trading-strategy\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"footer-link\",\n            children: \"GitHub\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"mailto:<EMAIL>\",\n            className: \"footer-link\",\n            children: \"Support\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"ONnsyFuN4/uYij5wcK66OG8F4O8=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "MomentumDashboard", "AdvancedAnalytics", "useWebSocket", "jsxDEV", "_jsxDEV", "APP_CONFIG", "title", "version", "refreshInterval", "maxRetries", "App", "_s", "isLoading", "setIsLoading", "error", "setError", "connectionStatus", "setConnectionStatus", "lastUpdated", "setLastUpdated", "Date", "initializeApp", "Promise", "resolve", "setTimeout", "checkSystemRequirements", "setupPerformanceMonitoring", "console", "log", "err", "message", "requirements", "localStorage", "Storage", "fetch", "promises", "es6", "Symbol", "missingFeatures", "Object", "entries", "filter", "feature", "supported", "map", "length", "Error", "join", "window", "innerWidth", "warn", "performance", "checkMemory", "memory", "memoryUsage", "used", "Math", "round", "usedJSHeapSize", "total", "totalJSHeapSize", "limit", "jsHeapSizeLimit", "memoryInterval", "setInterval", "clearInterval", "updateOnlineStatus", "navigator", "onLine", "addEventListener", "removeEventListener", "handleError", "errorInfo", "handleRetry", "location", "reload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "href", "userAgent", "toISOString", "screen", "width", "height", "innerHeight", "process", "env", "NODE_ENV", "toLocaleTimeString", "onError", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/App.js"], "sourcesContent": ["/**\n * Main Application Component\n * =========================\n * \n * Root component for the Momentum Strategy Dashboard.\n * Handles application-level state, routing, and layout.\n */\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport MomentumDashboard from './components/MomentumDashboard';\nimport AdvancedAnalytics from './components/AdvancedAnalytics';\nimport useWebSocket from './hooks/useWebSocket';\nimport './App.css';\n\n// App-level configuration\nconst APP_CONFIG = {\n  title: 'Momentum Strategy Dashboard',\n  version: '1.0.0',\n  refreshInterval: 30000, // 30 seconds\n  maxRetries: 3\n};\n\nfunction App() {\n  // Application state\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [connectionStatus, setConnectionStatus] = useState('connected');\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  // Initialize application\n  useEffect(() => {\n    const initializeApp = async () => {\n      try {\n        // Simulate initialization delay\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        // Check system requirements\n        checkSystemRequirements();\n        \n        // Set up performance monitoring\n        setupPerformanceMonitoring();\n        \n        // Mark as loaded\n        setIsLoading(false);\n        setLastUpdated(new Date());\n        \n        console.log('✅ Dashboard initialized successfully');\n        \n      } catch (err) {\n        console.error('❌ Failed to initialize dashboard:', err);\n        setError(err.message);\n        setIsLoading(false);\n      }\n    };\n\n    initializeApp();\n  }, []);\n\n  // Check browser compatibility and system requirements\n  const checkSystemRequirements = useCallback(() => {\n    const requirements = {\n      localStorage: typeof(Storage) !== 'undefined',\n      fetch: typeof fetch !== 'undefined',\n      promises: typeof Promise !== 'undefined',\n      es6: typeof Symbol !== 'undefined'\n    };\n\n    const missingFeatures = Object.entries(requirements)\n      .filter(([feature, supported]) => !supported)\n      .map(([feature]) => feature);\n\n    if (missingFeatures.length > 0) {\n      throw new Error(`Unsupported browser. Missing features: ${missingFeatures.join(', ')}`);\n    }\n\n    // Check for minimum screen size\n    if (window.innerWidth < 320) {\n      console.warn('⚠️ Small screen detected. Dashboard may have limited functionality.');\n    }\n  }, []);\n\n  // Set up performance monitoring\n  const setupPerformanceMonitoring = useCallback(() => {\n    // Monitor memory usage\n    if ('memory' in performance) {\n      const checkMemory = () => {\n        const memory = performance.memory;\n        const memoryUsage = {\n          used: Math.round(memory.usedJSHeapSize / 1048576), // MB\n          total: Math.round(memory.totalJSHeapSize / 1048576), // MB\n          limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB\n        };\n        \n        // Warn if memory usage is high\n        if (memoryUsage.used / memoryUsage.limit > 0.8) {\n          console.warn('⚠️ High memory usage detected:', memoryUsage);\n        }\n      };\n\n      // Check memory every 5 minutes\n      const memoryInterval = setInterval(checkMemory, 300000);\n      \n      // Cleanup on unmount\n      return () => clearInterval(memoryInterval);\n    }\n\n    // Monitor network status\n    const updateOnlineStatus = () => {\n      setConnectionStatus(navigator.onLine ? 'connected' : 'disconnected');\n    };\n\n    window.addEventListener('online', updateOnlineStatus);\n    window.addEventListener('offline', updateOnlineStatus);\n\n    // Cleanup event listeners\n    return () => {\n      window.removeEventListener('online', updateOnlineStatus);\n      window.removeEventListener('offline', updateOnlineStatus);\n    };\n  }, []);\n\n  // Handle application errors\n  const handleError = useCallback((error, errorInfo) => {\n    console.error('Application Error:', error, errorInfo);\n    setError(error.message);\n    \n    // In production, you might want to send this to an error reporting service\n    // errorReportingService.captureException(error, { extra: errorInfo });\n  }, []);\n\n  // Retry functionality for failed operations\n  const handleRetry = useCallback(() => {\n    setError(null);\n    setIsLoading(true);\n    window.location.reload();\n  }, []);\n\n  // Loading screen\n  if (isLoading) {\n    return (\n      <div className=\"app-loading\">\n        <div className=\"loading-content\">\n          <div className=\"loading-spinner\"></div>\n          <h2 className=\"loading-title\">Loading Strategy Dashboard</h2>\n          <p className=\"loading-subtitle\">Initializing quantitative analytics...</p>\n          <div className=\"loading-progress\">\n            <div className=\"loading-bar\"></div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Error screen\n  if (error) {\n    return (\n      <div className=\"app-error\">\n        <div className=\"error-content\">\n          <div className=\"error-icon\">⚠️</div>\n          <h2 className=\"error-title\">Dashboard Error</h2>\n          <p className=\"error-message\">{error}</p>\n          <div className=\"error-actions\">\n            <button \n              className=\"btn btn-primary\" \n              onClick={handleRetry}\n            >\n              Retry\n            </button>\n            <button \n              className=\"btn btn-secondary\" \n              onClick={() => window.location.href = 'mailto:<EMAIL>'}\n            >\n              Contact Support\n            </button>\n          </div>\n          <details className=\"error-details\">\n            <summary>Technical Information</summary>\n            <pre className=\"error-stack\">\n              User Agent: {navigator.userAgent}\n              {'\\n'}Timestamp: {new Date().toISOString()}\n              {'\\n'}Screen: {window.screen.width}x{window.screen.height}\n              {'\\n'}Viewport: {window.innerWidth}x{window.innerHeight}\n            </pre>\n          </details>\n        </div>\n      </div>\n    );\n  }\n\n  // Main application\n  return (\n    <div className=\"app\">\n      {/* Status bar for development */}\n      {process.env.NODE_ENV === 'development' && (\n        <div className=\"status-bar\">\n          <span className={`status-indicator ${connectionStatus}`}>\n            {connectionStatus === 'connected' ? '🟢' : '🔴'} {connectionStatus}\n          </span>\n          <span className=\"last-updated\">\n            Last updated: {lastUpdated.toLocaleTimeString()}\n          </span>\n          <span className=\"version\">\n            v{APP_CONFIG.version}\n          </span>\n        </div>\n      )}\n\n      {/* Main dashboard */}\n      <main className=\"app-main\">\n        <MomentumDashboard \n          onError={handleError}\n          connectionStatus={connectionStatus}\n          lastUpdated={lastUpdated}\n        />\n      </main>\n\n      {/* Footer */}\n      <footer className=\"app-footer\">\n        <div className=\"footer-content\">\n          <p className=\"footer-text\">\n            © 2024 Momentum Strategy Dashboard by Rishabh Ashok Patil. Built with React & Recharts for quantitative finance.\n          </p>\n          <div className=\"footer-links\">\n            <a \n              href=\"https://github.com/yourusername/momentum-trading-strategy\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              className=\"footer-link\"\n            >\n              GitHub\n            </a>\n            <a \n              href=\"mailto:<EMAIL>\" \n              className=\"footer-link\"\n            >\n              Support\n            </a>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,UAAU,GAAG;EACjBC,KAAK,EAAE,6BAA6B;EACpCC,OAAO,EAAE,OAAO;EAChBC,eAAe,EAAE,KAAK;EAAE;EACxBC,UAAU,EAAE;AACd,CAAC;AAED,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,WAAW,CAAC;EACrE,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,IAAIuB,IAAI,CAAC,CAAC,CAAC;;EAE1D;EACAtB,SAAS,CAAC,MAAM;IACd,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACAE,uBAAuB,CAAC,CAAC;;QAEzB;QACAC,0BAA0B,CAAC,CAAC;;QAE5B;QACAb,YAAY,CAAC,KAAK,CAAC;QACnBM,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;QAE1BO,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAErD,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZF,OAAO,CAACb,KAAK,CAAC,mCAAmC,EAAEe,GAAG,CAAC;QACvDd,QAAQ,CAACc,GAAG,CAACC,OAAO,CAAC;QACrBjB,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDQ,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,uBAAuB,GAAG1B,WAAW,CAAC,MAAM;IAChD,MAAMgC,YAAY,GAAG;MACnBC,YAAY,EAAE,OAAOC,OAAQ,KAAK,WAAW;MAC7CC,KAAK,EAAE,OAAOA,KAAK,KAAK,WAAW;MACnCC,QAAQ,EAAE,OAAOb,OAAO,KAAK,WAAW;MACxCc,GAAG,EAAE,OAAOC,MAAM,KAAK;IACzB,CAAC;IAED,MAAMC,eAAe,GAAGC,MAAM,CAACC,OAAO,CAACT,YAAY,CAAC,CACjDU,MAAM,CAAC,CAAC,CAACC,OAAO,EAAEC,SAAS,CAAC,KAAK,CAACA,SAAS,CAAC,CAC5CC,GAAG,CAAC,CAAC,CAACF,OAAO,CAAC,KAAKA,OAAO,CAAC;IAE9B,IAAIJ,eAAe,CAACO,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,0CAA0CR,eAAe,CAACS,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACzF;;IAEA;IACA,IAAIC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;MAC3BtB,OAAO,CAACuB,IAAI,CAAC,qEAAqE,CAAC;IACrF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMxB,0BAA0B,GAAG3B,WAAW,CAAC,MAAM;IACnD;IACA,IAAI,QAAQ,IAAIoD,WAAW,EAAE;MAC3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,MAAM,GAAGF,WAAW,CAACE,MAAM;QACjC,MAAMC,WAAW,GAAG;UAClBC,IAAI,EAAEC,IAAI,CAACC,KAAK,CAACJ,MAAM,CAACK,cAAc,GAAG,OAAO,CAAC;UAAE;UACnDC,KAAK,EAAEH,IAAI,CAACC,KAAK,CAACJ,MAAM,CAACO,eAAe,GAAG,OAAO,CAAC;UAAE;UACrDC,KAAK,EAAEL,IAAI,CAACC,KAAK,CAACJ,MAAM,CAACS,eAAe,GAAG,OAAO,CAAC,CAAC;QACtD,CAAC;;QAED;QACA,IAAIR,WAAW,CAACC,IAAI,GAAGD,WAAW,CAACO,KAAK,GAAG,GAAG,EAAE;UAC9ClC,OAAO,CAACuB,IAAI,CAAC,gCAAgC,EAAEI,WAAW,CAAC;QAC7D;MACF,CAAC;;MAED;MACA,MAAMS,cAAc,GAAGC,WAAW,CAACZ,WAAW,EAAE,MAAM,CAAC;;MAEvD;MACA,OAAO,MAAMa,aAAa,CAACF,cAAc,CAAC;IAC5C;;IAEA;IACA,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;MAC/BjD,mBAAmB,CAACkD,SAAS,CAACC,MAAM,GAAG,WAAW,GAAG,cAAc,CAAC;IACtE,CAAC;IAEDpB,MAAM,CAACqB,gBAAgB,CAAC,QAAQ,EAAEH,kBAAkB,CAAC;IACrDlB,MAAM,CAACqB,gBAAgB,CAAC,SAAS,EAAEH,kBAAkB,CAAC;;IAEtD;IACA,OAAO,MAAM;MACXlB,MAAM,CAACsB,mBAAmB,CAAC,QAAQ,EAAEJ,kBAAkB,CAAC;MACxDlB,MAAM,CAACsB,mBAAmB,CAAC,SAAS,EAAEJ,kBAAkB,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,WAAW,GAAGxE,WAAW,CAAC,CAACe,KAAK,EAAE0D,SAAS,KAAK;IACpD7C,OAAO,CAACb,KAAK,CAAC,oBAAoB,EAAEA,KAAK,EAAE0D,SAAS,CAAC;IACrDzD,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;;IAEvB;IACA;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2C,WAAW,GAAG1E,WAAW,CAAC,MAAM;IACpCgB,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,IAAI,CAAC;IAClBmC,MAAM,CAAC0B,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI/D,SAAS,EAAE;IACb,oBACER,OAAA;MAAKwE,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BzE,OAAA;QAAKwE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzE,OAAA;UAAKwE,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC7E,OAAA;UAAIwE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7D7E,OAAA;UAAGwE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1E7E,OAAA;UAAKwE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BzE,OAAA;YAAKwE,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAInE,KAAK,EAAE;IACT,oBACEV,OAAA;MAAKwE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBzE,OAAA;QAAKwE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzE,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpC7E,OAAA;UAAIwE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD7E,OAAA;UAAGwE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE/D;QAAK;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxC7E,OAAA;UAAKwE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzE,OAAA;YACEwE,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAET,WAAY;YAAAI,QAAA,EACtB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7E,OAAA;YACEwE,SAAS,EAAC,mBAAmB;YAC7BM,OAAO,EAAEA,CAAA,KAAMlC,MAAM,CAAC0B,QAAQ,CAACS,IAAI,GAAG,4BAA6B;YAAAN,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN7E,OAAA;UAASwE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAChCzE,OAAA;YAAAyE,QAAA,EAAS;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACxC7E,OAAA;YAAKwE,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,cACf,EAACV,SAAS,CAACiB,SAAS,EAC/B,IAAI,EAAC,aAAW,EAAC,IAAIhE,IAAI,CAAC,CAAC,CAACiE,WAAW,CAAC,CAAC,EACzC,IAAI,EAAC,UAAQ,EAACrC,MAAM,CAACsC,MAAM,CAACC,KAAK,EAAC,GAAC,EAACvC,MAAM,CAACsC,MAAM,CAACE,MAAM,EACxD,IAAI,EAAC,YAAU,EAACxC,MAAM,CAACC,UAAU,EAAC,GAAC,EAACD,MAAM,CAACyC,WAAW;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACE7E,OAAA;IAAKwE,SAAS,EAAC,KAAK;IAAAC,QAAA,GAEjBa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCxF,OAAA;MAAKwE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBzE,OAAA;QAAMwE,SAAS,EAAE,oBAAoB5D,gBAAgB,EAAG;QAAA6D,QAAA,GACrD7D,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,EAAC,GAAC,EAACA,gBAAgB;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACP7E,OAAA;QAAMwE,SAAS,EAAC,cAAc;QAAAC,QAAA,GAAC,gBACf,EAAC3D,WAAW,CAAC2E,kBAAkB,CAAC,CAAC;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACP7E,OAAA;QAAMwE,SAAS,EAAC,SAAS;QAAAC,QAAA,GAAC,GACvB,EAACxE,UAAU,CAACE,OAAO;MAAA;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAGD7E,OAAA;MAAMwE,SAAS,EAAC,UAAU;MAAAC,QAAA,eACxBzE,OAAA,CAACJ,iBAAiB;QAChB8F,OAAO,EAAEvB,WAAY;QACrBvD,gBAAgB,EAAEA,gBAAiB;QACnCE,WAAW,EAAEA;MAAY;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP7E,OAAA;MAAQwE,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BzE,OAAA;QAAKwE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzE,OAAA;UAAGwE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7E,OAAA;UAAKwE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzE,OAAA;YACE+E,IAAI,EAAC,2DAA2D;YAChEY,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBpB,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ7E,OAAA;YACE+E,IAAI,EAAC,4BAA4B;YACjCP,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACtE,EAAA,CA5NQD,GAAG;AAAAuF,EAAA,GAAHvF,GAAG;AA8NZ,eAAeA,GAAG;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}