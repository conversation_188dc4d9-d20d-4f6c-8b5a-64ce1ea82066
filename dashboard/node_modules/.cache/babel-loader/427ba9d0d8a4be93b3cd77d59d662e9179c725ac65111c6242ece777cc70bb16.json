{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/components/AdvancedAnalytics.js\",\n  _s = $RefreshSig$();\n/**\n * Advanced Analytics Component\n * ===========================\n * \n * Advanced analytics dashboard with machine learning insights,\n * factor analysis, regime detection, and predictive modeling.\n * \n * Author: <PERSON><PERSON>ab<PERSON>il\n */\n\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { LineChart, Line, AreaChart, Area, BarChart, Bar, ScatterPlot, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';\nimport { TrendingUp, Brain, Target, AlertTriangle, Activity, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdvancedAnalytics = ({\n  strategyData,\n  isConnected\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('factor-analysis');\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Fetch advanced analytics data\n  useEffect(() => {\n    const fetchAnalytics = async () => {\n      if (!isConnected) return;\n      setLoading(true);\n      try {\n        const response = await fetch('http://localhost:9000/api/advanced-analytics');\n        if (response.ok) {\n          const data = await response.json();\n          setAnalyticsData(data);\n          setError(null);\n        } else {\n          // Generate mock data for demonstration\n          setAnalyticsData(generateMockAnalytics());\n        }\n      } catch (err) {\n        console.error('Failed to fetch analytics:', err);\n        setAnalyticsData(generateMockAnalytics());\n        setError(null); // Use mock data instead of showing error\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchAnalytics();\n  }, [isConnected, strategyData]);\n\n  // Generate mock analytics data\n  const generateMockAnalytics = () => {\n    return {\n      factor_analysis: {\n        explained_variance_ratio: [0.45, 0.23, 0.15, 0.10, 0.07],\n        cumulative_variance: [0.45, 0.68, 0.83, 0.93, 1.00],\n        factor_loadings: {\n          'Factor_1': {\n            'SPY': 0.85,\n            'QQQ': 0.78,\n            'IWM': 0.72,\n            'VTI': 0.88\n          },\n          'Factor_2': {\n            'SPY': 0.32,\n            'QQQ': -0.45,\n            'IWM': 0.67,\n            'VTI': 0.28\n          },\n          'Factor_3': {\n            'SPY': -0.15,\n            'QQQ': 0.52,\n            'IWM': -0.38,\n            'VTI': -0.22\n          }\n        }\n      },\n      regime_detection: {\n        regimes: {\n          'regime_0': {\n            count: 120,\n            percentage: 35.2,\n            avg_return: 0.15,\n            volatility: 0.12,\n            sharpe_ratio: 1.25\n          },\n          'regime_1': {\n            count: 95,\n            percentage: 27.9,\n            avg_return: -0.08,\n            volatility: 0.25,\n            sharpe_ratio: -0.32\n          },\n          'regime_2': {\n            count: 126,\n            percentage: 36.9,\n            avg_return: 0.08,\n            volatility: 0.08,\n            sharpe_ratio: 1.00\n          }\n        }\n      },\n      predictive_model: {\n        model_performance: {\n          train_r2: 0.72,\n          test_r2: 0.58,\n          train_mse: 0.0012,\n          test_mse: 0.0018\n        },\n        feature_importance: [{\n          feature: 'SPY_momentum_20',\n          importance: 0.18\n        }, {\n          feature: 'market_volatility',\n          importance: 0.15\n        }, {\n          feature: 'QQQ_vol_20',\n          importance: 0.12\n        }, {\n          feature: 'VTI_rsi',\n          importance: 0.11\n        }, {\n          feature: 'market_momentum',\n          importance: 0.10\n        }]\n      },\n      risk_attribution: {\n        portfolio_volatility: 0.142,\n        diversification_ratio: 1.23,\n        risk_contributions: {\n          'SPY': {\n            position_weight: 0.25,\n            risk_contribution_pct: 28.5\n          },\n          'QQQ': {\n            position_weight: 0.20,\n            risk_contribution_pct: 24.2\n          },\n          'VTI': {\n            position_weight: 0.18,\n            risk_contribution_pct: 19.8\n          },\n          'IWM': {\n            position_weight: 0.15,\n            risk_contribution_pct: 16.1\n          }\n        }\n      }\n    };\n  };\n\n  // Tab configuration\n  const tabs = [{\n    id: 'factor-analysis',\n    label: 'Factor Analysis',\n    icon: TrendingUp\n  }, {\n    id: 'regime-detection',\n    label: 'Market Regimes',\n    icon: Activity\n  }, {\n    id: 'predictive-model',\n    label: 'ML Predictions',\n    icon: Brain\n  }, {\n    id: 'risk-attribution',\n    label: 'Risk Attribution',\n    icon: AlertTriangle\n  }];\n\n  // Render factor analysis\n  const renderFactorAnalysis = () => {\n    if (!(analyticsData !== null && analyticsData !== void 0 && analyticsData.factor_analysis)) return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"No factor analysis data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 49\n    }, this);\n    const {\n      explained_variance_ratio,\n      cumulative_variance,\n      factor_loadings\n    } = analyticsData.factor_analysis;\n    const varianceData = explained_variance_ratio.map((ratio, index) => ({\n      factor: `Factor ${index + 1}`,\n      individual: ratio * 100,\n      cumulative: cumulative_variance[index] * 100\n    }));\n    const loadingsData = Object.keys(factor_loadings.Factor_1 || {}).map(asset => {\n      var _factor_loadings$Fact, _factor_loadings$Fact2, _factor_loadings$Fact3;\n      return {\n        asset,\n        factor1: ((_factor_loadings$Fact = factor_loadings.Factor_1) === null || _factor_loadings$Fact === void 0 ? void 0 : _factor_loadings$Fact[asset]) || 0,\n        factor2: ((_factor_loadings$Fact2 = factor_loadings.Factor_2) === null || _factor_loadings$Fact2 === void 0 ? void 0 : _factor_loadings$Fact2[asset]) || 0,\n        factor3: ((_factor_loadings$Fact3 = factor_loadings.Factor_3) === null || _factor_loadings$Fact3 === void 0 ? void 0 : _factor_loadings$Fact3[asset]) || 0\n      };\n    });\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Explained Variance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: varianceData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"factor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                formatter: value => `${value.toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"individual\",\n                fill: \"#3b82f6\",\n                name: \"Individual\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                dataKey: \"cumulative\",\n                stroke: \"#ef4444\",\n                name: \"Cumulative\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Factor Loadings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: loadingsData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"asset\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"factor1\",\n                fill: \"#3b82f6\",\n                name: \"Factor 1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"factor2\",\n                fill: \"#10b981\",\n                name: \"Factor 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"factor3\",\n                fill: \"#f59e0b\",\n                name: \"Factor 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render regime detection\n  const renderRegimeDetection = () => {\n    if (!(analyticsData !== null && analyticsData !== void 0 && analyticsData.regime_detection)) return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"No regime data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 50\n    }, this);\n    const {\n      regimes\n    } = analyticsData.regime_detection;\n    const regimeData = Object.entries(regimes).map(([key, data]) => ({\n      regime: key.replace('regime_', 'Regime '),\n      percentage: data.percentage,\n      return: data.avg_return * 100,\n      volatility: data.volatility * 100,\n      sharpe: data.sharpe_ratio\n    }));\n    const COLORS = ['#3b82f6', '#ef4444', '#10b981'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Regime Distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: regimeData,\n                cx: \"50%\",\n                cy: \"50%\",\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"percentage\",\n                label: ({\n                  regime,\n                  percentage\n                }) => `${regime}: ${percentage.toFixed(1)}%`,\n                children: regimeData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[index % COLORS.length]\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Regime Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: regimeData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"regime\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"return\",\n                fill: \"#3b82f6\",\n                name: \"Return %\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"volatility\",\n                fill: \"#ef4444\",\n                name: \"Volatility %\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render predictive model\n  const renderPredictiveModel = () => {\n    if (!(analyticsData !== null && analyticsData !== void 0 && analyticsData.predictive_model)) return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"No model data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 50\n    }, this);\n    const {\n      model_performance,\n      feature_importance\n    } = analyticsData.predictive_model;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Train R\\xB2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-blue-600\",\n                children: [(model_performance.train_r2 * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Target, {\n              className: \"h-8 w-8 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Test R\\xB2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-green-600\",\n                children: [(model_performance.test_r2 * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Zap, {\n              className: \"h-8 w-8 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Test MSE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-orange-600\",\n                children: (model_performance.test_mse * 1000).toFixed(2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: \"h-8 w-8 text-orange-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4\",\n          children: \"Feature Importance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 300,\n          children: /*#__PURE__*/_jsxDEV(BarChart, {\n            data: feature_importance,\n            layout: \"horizontal\",\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              type: \"number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n              dataKey: \"feature\",\n              type: \"category\",\n              width: 120\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              formatter: value => `${(value * 100).toFixed(1)}%`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: \"importance\",\n              fill: \"#3b82f6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render risk attribution\n  const renderRiskAttribution = () => {\n    if (!(analyticsData !== null && analyticsData !== void 0 && analyticsData.risk_attribution)) return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"No risk data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 50\n    }, this);\n    const {\n      risk_contributions,\n      portfolio_volatility,\n      diversification_ratio\n    } = analyticsData.risk_attribution;\n    const riskData = Object.entries(risk_contributions).map(([asset, data]) => ({\n      asset,\n      weight: data.position_weight * 100,\n      risk: data.risk_contribution_pct\n    }));\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Portfolio Volatility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-red-600\",\n                children: [(portfolio_volatility * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Activity, {\n              className: \"h-8 w-8 text-red-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Diversification Ratio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-green-600\",\n                children: diversification_ratio.toFixed(2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-8 w-8 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4\",\n          children: \"Risk Contribution vs Position Weight\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 300,\n          children: /*#__PURE__*/_jsxDEV(BarChart, {\n            data: riskData,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"asset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              formatter: value => `${value.toFixed(1)}%`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: \"weight\",\n              fill: \"#3b82f6\",\n              name: \"Position Weight %\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: \"risk\",\n              fill: \"#ef4444\",\n              name: \"Risk Contribution %\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: \"Loading advanced analytics...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Advanced Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Machine learning insights and factor analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 mt-1\",\n            children: \"By Rishabh Ashok Patil\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Brain, {\n          className: \"h-8 w-8 text-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"-mb-px flex space-x-8 px-6\",\n          children: tabs.map(tab => {\n            const Icon = tab.icon;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab.id),\n              className: `py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: tab.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [activeTab === 'factor-analysis' && renderFactorAnalysis(), activeTab === 'regime-detection' && renderRegimeDetection(), activeTab === 'predictive-model' && renderPredictiveModel(), activeTab === 'risk-attribution' && renderRiskAttribution()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 350,\n    columnNumber: 5\n  }, this);\n};\n_s(AdvancedAnalytics, \"JL/9TWHPb/3dAW67712w/V4VQ5Q=\");\n_c = AdvancedAnalytics;\nexport default AdvancedAnalytics;\nvar _c;\n$RefreshReg$(_c, \"AdvancedAnalytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "Line<PERSON>hart", "Line", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Bar", "ScatterPlot", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "RadarChart", "PolarGrid", "PolarAngleAxis", "PolarRadiusAxis", "Radar", "TrendingUp", "Brain", "Target", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Activity", "Zap", "jsxDEV", "_jsxDEV", "AdvancedAnalytics", "strategyData", "isConnected", "_s", "activeTab", "setActiveTab", "analyticsData", "setAnalyticsData", "loading", "setLoading", "error", "setError", "fetchAnalytics", "response", "fetch", "ok", "data", "json", "generateMockAnalytics", "err", "console", "factor_analysis", "explained_variance_ratio", "cumulative_variance", "factor_loadings", "regime_detection", "regimes", "count", "percentage", "avg_return", "volatility", "sharpe_ratio", "predictive_model", "model_performance", "train_r2", "test_r2", "train_mse", "test_mse", "feature_importance", "feature", "importance", "risk_attribution", "portfolio_volatility", "diversification_ratio", "risk_contributions", "position_weight", "risk_contribution_pct", "tabs", "id", "label", "icon", "renderFactorAnalysis", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "varianceData", "map", "ratio", "index", "factor", "individual", "cumulative", "loadingsData", "Object", "keys", "Factor_1", "asset", "_factor_loadings$Fact", "_factor_loadings$Fact2", "_factor_loadings$Fact3", "factor1", "factor2", "Factor_2", "factor3", "Factor_3", "className", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "formatter", "value", "toFixed", "fill", "name", "stroke", "renderRegimeDetection", "regimeData", "entries", "key", "regime", "replace", "return", "sharpe", "COLORS", "cx", "cy", "outerRadius", "entry", "length", "renderPredictiveModel", "layout", "type", "renderRiskAttribution", "riskData", "weight", "risk", "tab", "Icon", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/components/AdvancedAnalytics.js"], "sourcesContent": ["/**\n * Advanced Analytics Component\n * ===========================\n * \n * Advanced analytics dashboard with machine learning insights,\n * factor analysis, regime detection, and predictive modeling.\n * \n * Author: <PERSON><PERSON><PERSON><PERSON>\n */\n\nimport React, { useState, useEffect, useMemo } from 'react';\nimport {\n  LineChart, Line, AreaChart, Area, BarChart, Bar, ScatterPlot,\n  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,\n  PieChart, Pie, Cell, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar\n} from 'recharts';\nimport { TrendingUp, Brain, Target, AlertTriangle, Activity, Zap } from 'lucide-react';\n\nconst AdvancedAnalytics = ({ strategyData, isConnected }) => {\n  const [activeTab, setActiveTab] = useState('factor-analysis');\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Fetch advanced analytics data\n  useEffect(() => {\n    const fetchAnalytics = async () => {\n      if (!isConnected) return;\n      \n      setLoading(true);\n      try {\n        const response = await fetch('http://localhost:9000/api/advanced-analytics');\n        if (response.ok) {\n          const data = await response.json();\n          setAnalyticsData(data);\n          setError(null);\n        } else {\n          // Generate mock data for demonstration\n          setAnalyticsData(generateMockAnalytics());\n        }\n      } catch (err) {\n        console.error('Failed to fetch analytics:', err);\n        setAnalyticsData(generateMockAnalytics());\n        setError(null); // Use mock data instead of showing error\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchAnalytics();\n  }, [isConnected, strategyData]);\n\n  // Generate mock analytics data\n  const generateMockAnalytics = () => {\n    return {\n      factor_analysis: {\n        explained_variance_ratio: [0.45, 0.23, 0.15, 0.10, 0.07],\n        cumulative_variance: [0.45, 0.68, 0.83, 0.93, 1.00],\n        factor_loadings: {\n          'Factor_1': { 'SPY': 0.85, 'QQQ': 0.78, 'IWM': 0.72, 'VTI': 0.88 },\n          'Factor_2': { 'SPY': 0.32, 'QQQ': -0.45, 'IWM': 0.67, 'VTI': 0.28 },\n          'Factor_3': { 'SPY': -0.15, 'QQQ': 0.52, 'IWM': -0.38, 'VTI': -0.22 }\n        }\n      },\n      regime_detection: {\n        regimes: {\n          'regime_0': { count: 120, percentage: 35.2, avg_return: 0.15, volatility: 0.12, sharpe_ratio: 1.25 },\n          'regime_1': { count: 95, percentage: 27.9, avg_return: -0.08, volatility: 0.25, sharpe_ratio: -0.32 },\n          'regime_2': { count: 126, percentage: 36.9, avg_return: 0.08, volatility: 0.08, sharpe_ratio: 1.00 }\n        }\n      },\n      predictive_model: {\n        model_performance: { train_r2: 0.72, test_r2: 0.58, train_mse: 0.0012, test_mse: 0.0018 },\n        feature_importance: [\n          { feature: 'SPY_momentum_20', importance: 0.18 },\n          { feature: 'market_volatility', importance: 0.15 },\n          { feature: 'QQQ_vol_20', importance: 0.12 },\n          { feature: 'VTI_rsi', importance: 0.11 },\n          { feature: 'market_momentum', importance: 0.10 }\n        ]\n      },\n      risk_attribution: {\n        portfolio_volatility: 0.142,\n        diversification_ratio: 1.23,\n        risk_contributions: {\n          'SPY': { position_weight: 0.25, risk_contribution_pct: 28.5 },\n          'QQQ': { position_weight: 0.20, risk_contribution_pct: 24.2 },\n          'VTI': { position_weight: 0.18, risk_contribution_pct: 19.8 },\n          'IWM': { position_weight: 0.15, risk_contribution_pct: 16.1 }\n        }\n      }\n    };\n  };\n\n  // Tab configuration\n  const tabs = [\n    { id: 'factor-analysis', label: 'Factor Analysis', icon: TrendingUp },\n    { id: 'regime-detection', label: 'Market Regimes', icon: Activity },\n    { id: 'predictive-model', label: 'ML Predictions', icon: Brain },\n    { id: 'risk-attribution', label: 'Risk Attribution', icon: AlertTriangle }\n  ];\n\n  // Render factor analysis\n  const renderFactorAnalysis = () => {\n    if (!analyticsData?.factor_analysis) return <div>No factor analysis data</div>;\n\n    const { explained_variance_ratio, cumulative_variance, factor_loadings } = analyticsData.factor_analysis;\n    \n    const varianceData = explained_variance_ratio.map((ratio, index) => ({\n      factor: `Factor ${index + 1}`,\n      individual: ratio * 100,\n      cumulative: cumulative_variance[index] * 100\n    }));\n\n    const loadingsData = Object.keys(factor_loadings.Factor_1 || {}).map(asset => ({\n      asset,\n      factor1: factor_loadings.Factor_1?.[asset] || 0,\n      factor2: factor_loadings.Factor_2?.[asset] || 0,\n      factor3: factor_loadings.Factor_3?.[asset] || 0\n    }));\n\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <h3 className=\"text-lg font-semibold mb-4\">Explained Variance</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={varianceData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"factor\" />\n                <YAxis />\n                <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />\n                <Legend />\n                <Bar dataKey=\"individual\" fill=\"#3b82f6\" name=\"Individual\" />\n                <Line dataKey=\"cumulative\" stroke=\"#ef4444\" name=\"Cumulative\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <h3 className=\"text-lg font-semibold mb-4\">Factor Loadings</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={loadingsData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"asset\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Bar dataKey=\"factor1\" fill=\"#3b82f6\" name=\"Factor 1\" />\n                <Bar dataKey=\"factor2\" fill=\"#10b981\" name=\"Factor 2\" />\n                <Bar dataKey=\"factor3\" fill=\"#f59e0b\" name=\"Factor 3\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // Render regime detection\n  const renderRegimeDetection = () => {\n    if (!analyticsData?.regime_detection) return <div>No regime data</div>;\n\n    const { regimes } = analyticsData.regime_detection;\n    \n    const regimeData = Object.entries(regimes).map(([key, data]) => ({\n      regime: key.replace('regime_', 'Regime '),\n      percentage: data.percentage,\n      return: data.avg_return * 100,\n      volatility: data.volatility * 100,\n      sharpe: data.sharpe_ratio\n    }));\n\n    const COLORS = ['#3b82f6', '#ef4444', '#10b981'];\n\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <h3 className=\"text-lg font-semibold mb-4\">Regime Distribution</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={regimeData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"percentage\"\n                  label={({ regime, percentage }) => `${regime}: ${percentage.toFixed(1)}%`}\n                >\n                  {regimeData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <h3 className=\"text-lg font-semibold mb-4\">Regime Performance</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={regimeData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"regime\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Bar dataKey=\"return\" fill=\"#3b82f6\" name=\"Return %\" />\n                <Bar dataKey=\"volatility\" fill=\"#ef4444\" name=\"Volatility %\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // Render predictive model\n  const renderPredictiveModel = () => {\n    if (!analyticsData?.predictive_model) return <div>No model data</div>;\n\n    const { model_performance, feature_importance } = analyticsData.predictive_model;\n\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Train R²</p>\n                <p className=\"text-2xl font-bold text-blue-600\">\n                  {(model_performance.train_r2 * 100).toFixed(1)}%\n                </p>\n              </div>\n              <Target className=\"h-8 w-8 text-blue-600\" />\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Test R²</p>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {(model_performance.test_r2 * 100).toFixed(1)}%\n                </p>\n              </div>\n              <Zap className=\"h-8 w-8 text-green-600\" />\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Test MSE</p>\n                <p className=\"text-2xl font-bold text-orange-600\">\n                  {(model_performance.test_mse * 1000).toFixed(2)}\n                </p>\n              </div>\n              <AlertTriangle className=\"h-8 w-8 text-orange-600\" />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n          <h3 className=\"text-lg font-semibold mb-4\">Feature Importance</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <BarChart data={feature_importance} layout=\"horizontal\">\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis type=\"number\" />\n              <YAxis dataKey=\"feature\" type=\"category\" width={120} />\n              <Tooltip formatter={(value) => `${(value * 100).toFixed(1)}%`} />\n              <Bar dataKey=\"importance\" fill=\"#3b82f6\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n    );\n  };\n\n  // Render risk attribution\n  const renderRiskAttribution = () => {\n    if (!analyticsData?.risk_attribution) return <div>No risk data</div>;\n\n    const { risk_contributions, portfolio_volatility, diversification_ratio } = analyticsData.risk_attribution;\n    \n    const riskData = Object.entries(risk_contributions).map(([asset, data]) => ({\n      asset,\n      weight: data.position_weight * 100,\n      risk: data.risk_contribution_pct\n    }));\n\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Portfolio Volatility</p>\n                <p className=\"text-2xl font-bold text-red-600\">\n                  {(portfolio_volatility * 100).toFixed(1)}%\n                </p>\n              </div>\n              <Activity className=\"h-8 w-8 text-red-600\" />\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Diversification Ratio</p>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {diversification_ratio.toFixed(2)}\n                </p>\n              </div>\n              <TrendingUp className=\"h-8 w-8 text-green-600\" />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n          <h3 className=\"text-lg font-semibold mb-4\">Risk Contribution vs Position Weight</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <BarChart data={riskData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"asset\" />\n              <YAxis />\n              <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />\n              <Legend />\n              <Bar dataKey=\"weight\" fill=\"#3b82f6\" name=\"Position Weight %\" />\n              <Bar dataKey=\"risk\" fill=\"#ef4444\" name=\"Risk Contribution %\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        <span className=\"ml-2\">Loading advanced analytics...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Advanced Analytics</h2>\n            <p className=\"text-gray-600\">Machine learning insights and factor analysis</p>\n            <p className=\"text-sm text-gray-500 mt-1\">By Rishabh Ashok Patil</p>\n          </div>\n          <Brain className=\"h-8 w-8 text-blue-600\" />\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white rounded-lg shadow-sm\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{tab.label}</span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {activeTab === 'factor-analysis' && renderFactorAnalysis()}\n          {activeTab === 'regime-detection' && renderRegimeDetection()}\n          {activeTab === 'predictive-model' && renderPredictiveModel()}\n          {activeTab === 'risk-attribution' && renderRiskAttribution()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdvancedAnalytics;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SACEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAC5DC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,EACjEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,KAAK,QAC7E,UAAU;AACjB,SAASC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvF,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,iBAAiB,CAAC;EAC7D,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2C,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACV,WAAW,EAAE;MAElBO,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,8CAA8C,CAAC;QAC5E,IAAID,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClCV,gBAAgB,CAACS,IAAI,CAAC;UACtBL,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACL;UACAJ,gBAAgB,CAACW,qBAAqB,CAAC,CAAC,CAAC;QAC3C;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAES,GAAG,CAAC;QAChDZ,gBAAgB,CAACW,qBAAqB,CAAC,CAAC,CAAC;QACzCP,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACV,WAAW,EAAED,YAAY,CAAC,CAAC;;EAE/B;EACA,MAAMiB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAO;MACLG,eAAe,EAAE;QACfC,wBAAwB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QACxDC,mBAAmB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QACnDC,eAAe,EAAE;UACf,UAAU,EAAE;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE;UAAK,CAAC;UAClE,UAAU,EAAE;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,CAAC,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE;UAAK,CAAC;UACnE,UAAU,EAAE;YAAE,KAAK,EAAE,CAAC,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,CAAC,IAAI;YAAE,KAAK,EAAE,CAAC;UAAK;QACtE;MACF,CAAC;MACDC,gBAAgB,EAAE;QAChBC,OAAO,EAAE;UACP,UAAU,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,UAAU,EAAE,IAAI;YAAEC,UAAU,EAAE,IAAI;YAAEC,UAAU,EAAE,IAAI;YAAEC,YAAY,EAAE;UAAK,CAAC;UACpG,UAAU,EAAE;YAAEJ,KAAK,EAAE,EAAE;YAAEC,UAAU,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,IAAI;YAAEC,UAAU,EAAE,IAAI;YAAEC,YAAY,EAAE,CAAC;UAAK,CAAC;UACrG,UAAU,EAAE;YAAEJ,KAAK,EAAE,GAAG;YAAEC,UAAU,EAAE,IAAI;YAAEC,UAAU,EAAE,IAAI;YAAEC,UAAU,EAAE,IAAI;YAAEC,YAAY,EAAE;UAAK;QACrG;MACF,CAAC;MACDC,gBAAgB,EAAE;QAChBC,iBAAiB,EAAE;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,IAAI;UAAEC,SAAS,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAC;QACzFC,kBAAkB,EAAE,CAClB;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,UAAU,EAAE;QAAK,CAAC,EAChD;UAAED,OAAO,EAAE,mBAAmB;UAAEC,UAAU,EAAE;QAAK,CAAC,EAClD;UAAED,OAAO,EAAE,YAAY;UAAEC,UAAU,EAAE;QAAK,CAAC,EAC3C;UAAED,OAAO,EAAE,SAAS;UAAEC,UAAU,EAAE;QAAK,CAAC,EACxC;UAAED,OAAO,EAAE,iBAAiB;UAAEC,UAAU,EAAE;QAAK,CAAC;MAEpD,CAAC;MACDC,gBAAgB,EAAE;QAChBC,oBAAoB,EAAE,KAAK;QAC3BC,qBAAqB,EAAE,IAAI;QAC3BC,kBAAkB,EAAE;UAClB,KAAK,EAAE;YAAEC,eAAe,EAAE,IAAI;YAAEC,qBAAqB,EAAE;UAAK,CAAC;UAC7D,KAAK,EAAE;YAAED,eAAe,EAAE,IAAI;YAAEC,qBAAqB,EAAE;UAAK,CAAC;UAC7D,KAAK,EAAE;YAAED,eAAe,EAAE,IAAI;YAAEC,qBAAqB,EAAE;UAAK,CAAC;UAC7D,KAAK,EAAE;YAAED,eAAe,EAAE,IAAI;YAAEC,qBAAqB,EAAE;UAAK;QAC9D;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,iBAAiB;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE1D;EAAW,CAAC,EACrE;IAAEwD,EAAE,EAAE,kBAAkB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAEtD;EAAS,CAAC,EACnE;IAAEoD,EAAE,EAAE,kBAAkB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAEzD;EAAM,CAAC,EAChE;IAAEuD,EAAE,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEvD;EAAc,CAAC,CAC3E;;EAED;EACA,MAAMwD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,EAAC7C,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEe,eAAe,GAAE,oBAAOtB,OAAA;MAAAqD,QAAA,EAAK;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAE9E,MAAM;MAAElC,wBAAwB;MAAEC,mBAAmB;MAAEC;IAAgB,CAAC,GAAGlB,aAAa,CAACe,eAAe;IAExG,MAAMoC,YAAY,GAAGnC,wBAAwB,CAACoC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;MACnEC,MAAM,EAAE,UAAUD,KAAK,GAAG,CAAC,EAAE;MAC7BE,UAAU,EAAEH,KAAK,GAAG,GAAG;MACvBI,UAAU,EAAExC,mBAAmB,CAACqC,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;IAEH,MAAMI,YAAY,GAAGC,MAAM,CAACC,IAAI,CAAC1C,eAAe,CAAC2C,QAAQ,IAAI,CAAC,CAAC,CAAC,CAACT,GAAG,CAACU,KAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAAA,OAAK;QAC7EH,KAAK;QACLI,OAAO,EAAE,EAAAH,qBAAA,GAAA7C,eAAe,CAAC2C,QAAQ,cAAAE,qBAAA,uBAAxBA,qBAAA,CAA2BD,KAAK,CAAC,KAAI,CAAC;QAC/CK,OAAO,EAAE,EAAAH,sBAAA,GAAA9C,eAAe,CAACkD,QAAQ,cAAAJ,sBAAA,uBAAxBA,sBAAA,CAA2BF,KAAK,CAAC,KAAI,CAAC;QAC/CO,OAAO,EAAE,EAAAJ,sBAAA,GAAA/C,eAAe,CAACoD,QAAQ,cAAAL,sBAAA,uBAAxBA,sBAAA,CAA2BH,KAAK,CAAC,KAAI;MAChD,CAAC;IAAA,CAAC,CAAC;IAEH,oBACErE,OAAA;MAAK8E,SAAS,EAAC,WAAW;MAAAzB,QAAA,eACxBrD,OAAA;QAAK8E,SAAS,EAAC,uCAAuC;QAAAzB,QAAA,gBACpDrD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,gBAChDrD,OAAA;YAAI8E,SAAS,EAAC,4BAA4B;YAAAzB,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClEzD,OAAA,CAAChB,mBAAmB;YAAC+F,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA3B,QAAA,eAC5CrD,OAAA,CAACxB,QAAQ;cAACyC,IAAI,EAAEyC,YAAa;cAAAL,QAAA,gBAC3BrD,OAAA,CAACnB,aAAa;gBAACoG,eAAe,EAAC;cAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCzD,OAAA,CAACrB,KAAK;gBAACuG,OAAO,EAAC;cAAQ;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1BzD,OAAA,CAACpB,KAAK;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTzD,OAAA,CAAClB,OAAO;gBAACqG,SAAS,EAAGC,KAAK,IAAK,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;cAAI;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDzD,OAAA,CAACjB,MAAM;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVzD,OAAA,CAACvB,GAAG;gBAACyG,OAAO,EAAC,YAAY;gBAACI,IAAI,EAAC,SAAS;gBAACC,IAAI,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DzD,OAAA,CAAC3B,IAAI;gBAAC6G,OAAO,EAAC,YAAY;gBAACM,MAAM,EAAC,SAAS;gBAACD,IAAI,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAENzD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,gBAChDrD,OAAA;YAAI8E,SAAS,EAAC,4BAA4B;YAAAzB,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DzD,OAAA,CAAChB,mBAAmB;YAAC+F,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA3B,QAAA,eAC5CrD,OAAA,CAACxB,QAAQ;cAACyC,IAAI,EAAEgD,YAAa;cAAAZ,QAAA,gBAC3BrD,OAAA,CAACnB,aAAa;gBAACoG,eAAe,EAAC;cAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCzD,OAAA,CAACrB,KAAK;gBAACuG,OAAO,EAAC;cAAO;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBzD,OAAA,CAACpB,KAAK;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTzD,OAAA,CAAClB,OAAO;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzD,OAAA,CAACjB,MAAM;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVzD,OAAA,CAACvB,GAAG;gBAACyG,OAAO,EAAC,SAAS;gBAACI,IAAI,EAAC,SAAS;gBAACC,IAAI,EAAC;cAAU;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDzD,OAAA,CAACvB,GAAG;gBAACyG,OAAO,EAAC,SAAS;gBAACI,IAAI,EAAC,SAAS;gBAACC,IAAI,EAAC;cAAU;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDzD,OAAA,CAACvB,GAAG;gBAACyG,OAAO,EAAC,SAAS;gBAACI,IAAI,EAAC,SAAS;gBAACC,IAAI,EAAC;cAAU;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,EAAClF,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEmB,gBAAgB,GAAE,oBAAO1B,OAAA;MAAAqD,QAAA,EAAK;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAEtE,MAAM;MAAE9B;IAAQ,CAAC,GAAGpB,aAAa,CAACmB,gBAAgB;IAElD,MAAMgE,UAAU,GAAGxB,MAAM,CAACyB,OAAO,CAAChE,OAAO,CAAC,CAACgC,GAAG,CAAC,CAAC,CAACiC,GAAG,EAAE3E,IAAI,CAAC,MAAM;MAC/D4E,MAAM,EAAED,GAAG,CAACE,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;MACzCjE,UAAU,EAAEZ,IAAI,CAACY,UAAU;MAC3BkE,MAAM,EAAE9E,IAAI,CAACa,UAAU,GAAG,GAAG;MAC7BC,UAAU,EAAEd,IAAI,CAACc,UAAU,GAAG,GAAG;MACjCiE,MAAM,EAAE/E,IAAI,CAACe;IACf,CAAC,CAAC,CAAC;IAEH,MAAMiE,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IAEhD,oBACEjG,OAAA;MAAK8E,SAAS,EAAC,WAAW;MAAAzB,QAAA,eACxBrD,OAAA;QAAK8E,SAAS,EAAC,uCAAuC;QAAAzB,QAAA,gBACpDrD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,gBAChDrD,OAAA;YAAI8E,SAAS,EAAC,4BAA4B;YAAAzB,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEzD,OAAA,CAAChB,mBAAmB;YAAC+F,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA3B,QAAA,eAC5CrD,OAAA,CAACf,QAAQ;cAAAoE,QAAA,gBACPrD,OAAA,CAACd,GAAG;gBACF+B,IAAI,EAAEyE,UAAW;gBACjBQ,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,WAAW,EAAE,EAAG;gBAChBd,IAAI,EAAC,SAAS;gBACdJ,OAAO,EAAC,YAAY;gBACpBhC,KAAK,EAAEA,CAAC;kBAAE2C,MAAM;kBAAEhE;gBAAW,CAAC,KAAK,GAAGgE,MAAM,KAAKhE,UAAU,CAACwD,OAAO,CAAC,CAAC,CAAC,GAAI;gBAAAhC,QAAA,EAEzEqC,UAAU,CAAC/B,GAAG,CAAC,CAAC0C,KAAK,EAAExC,KAAK,kBAC3B7D,OAAA,CAACb,IAAI;kBAAuBmG,IAAI,EAAEW,MAAM,CAACpC,KAAK,GAAGoC,MAAM,CAACK,MAAM;gBAAE,GAArD,QAAQzC,KAAK,EAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAwC,CACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNzD,OAAA,CAAClB,OAAO;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAENzD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,gBAChDrD,OAAA;YAAI8E,SAAS,EAAC,4BAA4B;YAAAzB,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClEzD,OAAA,CAAChB,mBAAmB;YAAC+F,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA3B,QAAA,eAC5CrD,OAAA,CAACxB,QAAQ;cAACyC,IAAI,EAAEyE,UAAW;cAAArC,QAAA,gBACzBrD,OAAA,CAACnB,aAAa;gBAACoG,eAAe,EAAC;cAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCzD,OAAA,CAACrB,KAAK;gBAACuG,OAAO,EAAC;cAAQ;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1BzD,OAAA,CAACpB,KAAK;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTzD,OAAA,CAAClB,OAAO;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzD,OAAA,CAACjB,MAAM;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVzD,OAAA,CAACvB,GAAG;gBAACyG,OAAO,EAAC,QAAQ;gBAACI,IAAI,EAAC,SAAS;gBAACC,IAAI,EAAC;cAAU;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDzD,OAAA,CAACvB,GAAG;gBAACyG,OAAO,EAAC,YAAY;gBAACI,IAAI,EAAC,SAAS;gBAACC,IAAI,EAAC;cAAc;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,EAAChG,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAE0B,gBAAgB,GAAE,oBAAOjC,OAAA;MAAAqD,QAAA,EAAK;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAErE,MAAM;MAAEvB,iBAAiB;MAAEK;IAAmB,CAAC,GAAGhC,aAAa,CAAC0B,gBAAgB;IAEhF,oBACEjC,OAAA;MAAK8E,SAAS,EAAC,WAAW;MAAAzB,QAAA,gBACxBrD,OAAA;QAAK8E,SAAS,EAAC,uCAAuC;QAAAzB,QAAA,gBACpDrD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,eAChDrD,OAAA;YAAK8E,SAAS,EAAC,mCAAmC;YAAAzB,QAAA,gBAChDrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAG8E,SAAS,EAAC,mCAAmC;gBAAAzB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7DzD,OAAA;gBAAG8E,SAAS,EAAC,kCAAkC;gBAAAzB,QAAA,GAC5C,CAACnB,iBAAiB,CAACC,QAAQ,GAAG,GAAG,EAAEkD,OAAO,CAAC,CAAC,CAAC,EAAC,GACjD;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA,CAACL,MAAM;cAACmF,SAAS,EAAC;YAAuB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,eAChDrD,OAAA;YAAK8E,SAAS,EAAC,mCAAmC;YAAAzB,QAAA,gBAChDrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAG8E,SAAS,EAAC,mCAAmC;gBAAAzB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5DzD,OAAA;gBAAG8E,SAAS,EAAC,mCAAmC;gBAAAzB,QAAA,GAC7C,CAACnB,iBAAiB,CAACE,OAAO,GAAG,GAAG,EAAEiD,OAAO,CAAC,CAAC,CAAC,EAAC,GAChD;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA,CAACF,GAAG;cAACgF,SAAS,EAAC;YAAwB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,eAChDrD,OAAA;YAAK8E,SAAS,EAAC,mCAAmC;YAAAzB,QAAA,gBAChDrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAG8E,SAAS,EAAC,mCAAmC;gBAAAzB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7DzD,OAAA;gBAAG8E,SAAS,EAAC,oCAAoC;gBAAAzB,QAAA,EAC9C,CAACnB,iBAAiB,CAACI,QAAQ,GAAG,IAAI,EAAE+C,OAAO,CAAC,CAAC;cAAC;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA,CAACJ,aAAa;cAACkF,SAAS,EAAC;YAAyB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAK8E,SAAS,EAAC,mCAAmC;QAAAzB,QAAA,gBAChDrD,OAAA;UAAI8E,SAAS,EAAC,4BAA4B;UAAAzB,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEzD,OAAA,CAAChB,mBAAmB;UAAC+F,KAAK,EAAC,MAAM;UAACC,MAAM,EAAE,GAAI;UAAA3B,QAAA,eAC5CrD,OAAA,CAACxB,QAAQ;YAACyC,IAAI,EAAEsB,kBAAmB;YAACiE,MAAM,EAAC,YAAY;YAAAnD,QAAA,gBACrDrD,OAAA,CAACnB,aAAa;cAACoG,eAAe,EAAC;YAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCzD,OAAA,CAACrB,KAAK;cAAC8H,IAAI,EAAC;YAAQ;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvBzD,OAAA,CAACpB,KAAK;cAACsG,OAAO,EAAC,SAAS;cAACuB,IAAI,EAAC,UAAU;cAAC1B,KAAK,EAAE;YAAI;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDzD,OAAA,CAAClB,OAAO;cAACqG,SAAS,EAAGC,KAAK,IAAK,GAAG,CAACA,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;YAAI;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjEzD,OAAA,CAACvB,GAAG;cAACyG,OAAO,EAAC,YAAY;cAACI,IAAI,EAAC;YAAS;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,EAACnG,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEmC,gBAAgB,GAAE,oBAAO1C,OAAA;MAAAqD,QAAA,EAAK;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAEpE,MAAM;MAAEZ,kBAAkB;MAAEF,oBAAoB;MAAEC;IAAsB,CAAC,GAAGrC,aAAa,CAACmC,gBAAgB;IAE1G,MAAMiE,QAAQ,GAAGzC,MAAM,CAACyB,OAAO,CAAC9C,kBAAkB,CAAC,CAACc,GAAG,CAAC,CAAC,CAACU,KAAK,EAAEpD,IAAI,CAAC,MAAM;MAC1EoD,KAAK;MACLuC,MAAM,EAAE3F,IAAI,CAAC6B,eAAe,GAAG,GAAG;MAClC+D,IAAI,EAAE5F,IAAI,CAAC8B;IACb,CAAC,CAAC,CAAC;IAEH,oBACE/C,OAAA;MAAK8E,SAAS,EAAC,WAAW;MAAAzB,QAAA,gBACxBrD,OAAA;QAAK8E,SAAS,EAAC,uCAAuC;QAAAzB,QAAA,gBACpDrD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,eAChDrD,OAAA;YAAK8E,SAAS,EAAC,mCAAmC;YAAAzB,QAAA,gBAChDrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAG8E,SAAS,EAAC,mCAAmC;gBAAAzB,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzEzD,OAAA;gBAAG8E,SAAS,EAAC,iCAAiC;gBAAAzB,QAAA,GAC3C,CAACV,oBAAoB,GAAG,GAAG,EAAE0C,OAAO,CAAC,CAAC,CAAC,EAAC,GAC3C;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA,CAACH,QAAQ;cAACiF,SAAS,EAAC;YAAsB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAzB,QAAA,eAChDrD,OAAA;YAAK8E,SAAS,EAAC,mCAAmC;YAAAzB,QAAA,gBAChDrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAG8E,SAAS,EAAC,mCAAmC;gBAAAzB,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1EzD,OAAA;gBAAG8E,SAAS,EAAC,mCAAmC;gBAAAzB,QAAA,EAC7CT,qBAAqB,CAACyC,OAAO,CAAC,CAAC;cAAC;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA,CAACP,UAAU;cAACqF,SAAS,EAAC;YAAwB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAK8E,SAAS,EAAC,mCAAmC;QAAAzB,QAAA,gBAChDrD,OAAA;UAAI8E,SAAS,EAAC,4BAA4B;UAAAzB,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFzD,OAAA,CAAChB,mBAAmB;UAAC+F,KAAK,EAAC,MAAM;UAACC,MAAM,EAAE,GAAI;UAAA3B,QAAA,eAC5CrD,OAAA,CAACxB,QAAQ;YAACyC,IAAI,EAAE0F,QAAS;YAAAtD,QAAA,gBACvBrD,OAAA,CAACnB,aAAa;cAACoG,eAAe,EAAC;YAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCzD,OAAA,CAACrB,KAAK;cAACuG,OAAO,EAAC;YAAO;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBzD,OAAA,CAACpB,KAAK;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTzD,OAAA,CAAClB,OAAO;cAACqG,SAAS,EAAGC,KAAK,IAAK,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;YAAI;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDzD,OAAA,CAACjB,MAAM;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVzD,OAAA,CAACvB,GAAG;cAACyG,OAAO,EAAC,QAAQ;cAACI,IAAI,EAAC,SAAS;cAACC,IAAI,EAAC;YAAmB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEzD,OAAA,CAACvB,GAAG;cAACyG,OAAO,EAAC,MAAM;cAACI,IAAI,EAAC,SAAS;cAACC,IAAI,EAAC;YAAqB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,IAAIhD,OAAO,EAAE;IACX,oBACET,OAAA;MAAK8E,SAAS,EAAC,uCAAuC;MAAAzB,QAAA,gBACpDrD,OAAA;QAAK8E,SAAS,EAAC;MAA8D;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpFzD,OAAA;QAAM8E,SAAS,EAAC,MAAM;QAAAzB,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC;EAEV;EAEA,oBACEzD,OAAA;IAAK8E,SAAS,EAAC,WAAW;IAAAzB,QAAA,gBAExBrD,OAAA;MAAK8E,SAAS,EAAC,mCAAmC;MAAAzB,QAAA,eAChDrD,OAAA;QAAK8E,SAAS,EAAC,mCAAmC;QAAAzB,QAAA,gBAChDrD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAI8E,SAAS,EAAC,kCAAkC;YAAAzB,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEzD,OAAA;YAAG8E,SAAS,EAAC,eAAe;YAAAzB,QAAA,EAAC;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9EzD,OAAA;YAAG8E,SAAS,EAAC,4BAA4B;YAAAzB,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACNzD,OAAA,CAACN,KAAK;UAACoF,SAAS,EAAC;QAAuB;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzD,OAAA;MAAK8E,SAAS,EAAC,+BAA+B;MAAAzB,QAAA,gBAC5CrD,OAAA;QAAK8E,SAAS,EAAC,0BAA0B;QAAAzB,QAAA,eACvCrD,OAAA;UAAK8E,SAAS,EAAC,4BAA4B;UAAAzB,QAAA,EACxCL,IAAI,CAACW,GAAG,CAAEmD,GAAG,IAAK;YACjB,MAAMC,IAAI,GAAGD,GAAG,CAAC3D,IAAI;YACrB,oBACEnD,OAAA;cAEEgH,OAAO,EAAEA,CAAA,KAAM1G,YAAY,CAACwG,GAAG,CAAC7D,EAAE,CAAE;cACpC6B,SAAS,EAAE,wEACTzE,SAAS,KAAKyG,GAAG,CAAC7D,EAAE,GAChB,+BAA+B,GAC/B,4EAA4E,EAC/E;cAAAI,QAAA,gBAEHrD,OAAA,CAAC+G,IAAI;gBAACjC,SAAS,EAAC;cAAS;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BzD,OAAA;gBAAAqD,QAAA,EAAOyD,GAAG,CAAC5D;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GATnBqD,GAAG,CAAC7D,EAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUL,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAK8E,SAAS,EAAC,KAAK;QAAAzB,QAAA,GACjBhD,SAAS,KAAK,iBAAiB,IAAI+C,oBAAoB,CAAC,CAAC,EACzD/C,SAAS,KAAK,kBAAkB,IAAIoF,qBAAqB,CAAC,CAAC,EAC3DpF,SAAS,KAAK,kBAAkB,IAAIkG,qBAAqB,CAAC,CAAC,EAC3DlG,SAAS,KAAK,kBAAkB,IAAIqG,qBAAqB,CAAC,CAAC;MAAA;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CAzXIH,iBAAiB;AAAAgH,EAAA,GAAjBhH,iBAAiB;AA2XvB,eAAeA,iBAAiB;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}