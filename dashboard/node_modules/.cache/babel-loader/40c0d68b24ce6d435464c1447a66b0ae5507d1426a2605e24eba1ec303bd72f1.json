{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * WebSocket Hook for Real-time Data\n * =================================\n * \n * Custom React hook for managing WebSocket connections and real-time data streaming.\n * Provides automatic reconnection, error handling, and data synchronization.\n * \n * Author: R<PERSON><PERSON><PERSON>\n */\n\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport io from 'socket.io-client';\nconst useWebSocket = (url = 'http://localhost:9001', options = {}) => {\n  _s();\n  // State management\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [lastMessage, setLastMessage] = useState(null);\n  const [strategyData, setStrategyData] = useState(null);\n  const [priceUpdates, setPriceUpdates] = useState({});\n\n  // Refs\n  const socketRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const reconnectAttempts = useRef(0);\n\n  // Configuration\n  const maxReconnectAttempts = options.maxReconnectAttempts || 5;\n  const reconnectInterval = options.reconnectInterval || 3000;\n\n  // Connect to WebSocket\n  const connect = useCallback(() => {\n    try {\n      console.log('🔌 Connecting to WebSocket:', url);\n      socketRef.current = io(url, {\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        forceNew: true,\n        ...options.socketOptions\n      });\n\n      // Connection event handlers\n      socketRef.current.on('connect', () => {\n        console.log('✅ WebSocket connected');\n        setIsConnected(true);\n        setConnectionError(null);\n        reconnectAttempts.current = 0;\n\n        // Subscribe to updates\n        socketRef.current.emit('subscribe_to_updates', {\n          types: ['strategy_data', 'price_updates', 'alerts']\n        });\n      });\n      socketRef.current.on('disconnect', reason => {\n        console.log('❌ WebSocket disconnected:', reason);\n        setIsConnected(false);\n\n        // Attempt reconnection if not manually disconnected\n        if (reason !== 'io client disconnect') {\n          scheduleReconnect();\n        }\n      });\n      socketRef.current.on('connect_error', error => {\n        console.error('🚫 WebSocket connection error:', error);\n        setConnectionError(error.message);\n        setIsConnected(false);\n        scheduleReconnect();\n      });\n\n      // Data event handlers\n      socketRef.current.on('strategy_data', data => {\n        console.log('📊 Received strategy data:', data);\n        setStrategyData(data);\n        setLastMessage({\n          type: 'strategy_data',\n          data,\n          timestamp: new Date().toISOString()\n        });\n      });\n      socketRef.current.on('price_updates', data => {\n        console.log('💰 Received price updates:', data);\n        setPriceUpdates(prevUpdates => ({\n          ...prevUpdates,\n          ...data\n        }));\n        setLastMessage({\n          type: 'price_updates',\n          data,\n          timestamp: new Date().toISOString()\n        });\n      });\n      socketRef.current.on('alert', data => {\n        console.log('🚨 Received alert:', data);\n        setLastMessage({\n          type: 'alert',\n          data,\n          timestamp: new Date().toISOString()\n        });\n      });\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionError(error.message);\n    }\n  }, [url, options.socketOptions]);\n\n  // Schedule reconnection\n  const scheduleReconnect = useCallback(() => {\n    if (reconnectAttempts.current < maxReconnectAttempts) {\n      reconnectAttempts.current += 1;\n      console.log(`🔄 Scheduling reconnection attempt ${reconnectAttempts.current}/${maxReconnectAttempts}`);\n      reconnectTimeoutRef.current = setTimeout(() => {\n        connect();\n      }, reconnectInterval * reconnectAttempts.current);\n    } else {\n      console.error('❌ Max reconnection attempts reached');\n      setConnectionError('Failed to reconnect after multiple attempts');\n    }\n  }, [connect, maxReconnectAttempts, reconnectInterval]);\n\n  // Disconnect from WebSocket\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    if (socketRef.current) {\n      socketRef.current.disconnect();\n      socketRef.current = null;\n    }\n    setIsConnected(false);\n    reconnectAttempts.current = 0;\n  }, []);\n\n  // Send message\n  const sendMessage = useCallback((event, data) => {\n    if (socketRef.current && isConnected) {\n      socketRef.current.emit(event, data);\n      return true;\n    } else {\n      console.warn('Cannot send message: WebSocket not connected');\n      return false;\n    }\n  }, [isConnected]);\n\n  // Request data refresh\n  const requestRefresh = useCallback(() => {\n    return sendMessage('request_refresh', {\n      timestamp: new Date().toISOString()\n    });\n  }, [sendMessage]);\n\n  // Initialize connection on mount\n  useEffect(() => {\n    if (options.autoConnect !== false) {\n      connect();\n    }\n\n    // Cleanup on unmount\n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect, options.autoConnect]);\n\n  // Heartbeat to maintain connection\n  useEffect(() => {\n    if (!isConnected) return;\n    const heartbeatInterval = setInterval(() => {\n      if (socketRef.current) {\n        socketRef.current.emit('ping');\n      }\n    }, 30000); // 30 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isConnected]);\n  return {\n    // Connection state\n    isConnected,\n    connectionError,\n    // Data\n    strategyData,\n    priceUpdates,\n    lastMessage,\n    // Actions\n    connect,\n    disconnect,\n    sendMessage,\n    requestRefresh,\n    // Connection info\n    reconnectAttempts: reconnectAttempts.current,\n    maxReconnectAttempts\n  };\n};\n_s(useWebSocket, \"3Yv/p1VOqpw2PZ2DGMaQ3JHhTnA=\");\nexport default useWebSocket;", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useCallback", "io", "useWebSocket", "url", "options", "_s", "isConnected", "setIsConnected", "connectionError", "setConnectionError", "lastMessage", "setLastMessage", "strategyData", "setStrategyData", "priceUpdates", "setPriceUpdates", "socketRef", "reconnectTimeoutRef", "reconnectAttempts", "maxReconnectAttempts", "reconnectInterval", "connect", "console", "log", "current", "transports", "timeout", "forceNew", "socketOptions", "on", "emit", "types", "reason", "scheduleReconnect", "error", "message", "data", "type", "timestamp", "Date", "toISOString", "prevUpdates", "setTimeout", "disconnect", "clearTimeout", "sendMessage", "event", "warn", "requestRefresh", "autoConnect", "heartbeatInterval", "setInterval", "clearInterval"], "sources": ["/Users/<USER>/Desktop/Quantitative Portfolio Projects/momentum-trading-strategy/dashboard/src/hooks/useWebSocket.js"], "sourcesContent": ["/**\n * WebSocket Hook for Real-time Data\n * =================================\n * \n * Custom React hook for managing WebSocket connections and real-time data streaming.\n * Provides automatic reconnection, error handling, and data synchronization.\n * \n * Author: <PERSON><PERSON><PERSON><PERSON>ok Patil\n */\n\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport io from 'socket.io-client';\n\nconst useWebSocket = (url = 'http://localhost:9001', options = {}) => {\n  // State management\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [lastMessage, setLastMessage] = useState(null);\n  const [strategyData, setStrategyData] = useState(null);\n  const [priceUpdates, setPriceUpdates] = useState({});\n  \n  // Refs\n  const socketRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const reconnectAttempts = useRef(0);\n  \n  // Configuration\n  const maxReconnectAttempts = options.maxReconnectAttempts || 5;\n  const reconnectInterval = options.reconnectInterval || 3000;\n  \n  // Connect to WebSocket\n  const connect = useCallback(() => {\n    try {\n      console.log('🔌 Connecting to WebSocket:', url);\n      \n      socketRef.current = io(url, {\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        forceNew: true,\n        ...options.socketOptions\n      });\n      \n      // Connection event handlers\n      socketRef.current.on('connect', () => {\n        console.log('✅ WebSocket connected');\n        setIsConnected(true);\n        setConnectionError(null);\n        reconnectAttempts.current = 0;\n        \n        // Subscribe to updates\n        socketRef.current.emit('subscribe_to_updates', {\n          types: ['strategy_data', 'price_updates', 'alerts']\n        });\n      });\n      \n      socketRef.current.on('disconnect', (reason) => {\n        console.log('❌ WebSocket disconnected:', reason);\n        setIsConnected(false);\n        \n        // Attempt reconnection if not manually disconnected\n        if (reason !== 'io client disconnect') {\n          scheduleReconnect();\n        }\n      });\n      \n      socketRef.current.on('connect_error', (error) => {\n        console.error('🚫 WebSocket connection error:', error);\n        setConnectionError(error.message);\n        setIsConnected(false);\n        scheduleReconnect();\n      });\n      \n      // Data event handlers\n      socketRef.current.on('strategy_data', (data) => {\n        console.log('📊 Received strategy data:', data);\n        setStrategyData(data);\n        setLastMessage({\n          type: 'strategy_data',\n          data,\n          timestamp: new Date().toISOString()\n        });\n      });\n      \n      socketRef.current.on('price_updates', (data) => {\n        console.log('💰 Received price updates:', data);\n        setPriceUpdates(prevUpdates => ({\n          ...prevUpdates,\n          ...data\n        }));\n        setLastMessage({\n          type: 'price_updates',\n          data,\n          timestamp: new Date().toISOString()\n        });\n      });\n      \n      socketRef.current.on('alert', (data) => {\n        console.log('🚨 Received alert:', data);\n        setLastMessage({\n          type: 'alert',\n          data,\n          timestamp: new Date().toISOString()\n        });\n      });\n      \n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionError(error.message);\n    }\n  }, [url, options.socketOptions]);\n  \n  // Schedule reconnection\n  const scheduleReconnect = useCallback(() => {\n    if (reconnectAttempts.current < maxReconnectAttempts) {\n      reconnectAttempts.current += 1;\n      console.log(`🔄 Scheduling reconnection attempt ${reconnectAttempts.current}/${maxReconnectAttempts}`);\n      \n      reconnectTimeoutRef.current = setTimeout(() => {\n        connect();\n      }, reconnectInterval * reconnectAttempts.current);\n    } else {\n      console.error('❌ Max reconnection attempts reached');\n      setConnectionError('Failed to reconnect after multiple attempts');\n    }\n  }, [connect, maxReconnectAttempts, reconnectInterval]);\n  \n  // Disconnect from WebSocket\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    \n    if (socketRef.current) {\n      socketRef.current.disconnect();\n      socketRef.current = null;\n    }\n    \n    setIsConnected(false);\n    reconnectAttempts.current = 0;\n  }, []);\n  \n  // Send message\n  const sendMessage = useCallback((event, data) => {\n    if (socketRef.current && isConnected) {\n      socketRef.current.emit(event, data);\n      return true;\n    } else {\n      console.warn('Cannot send message: WebSocket not connected');\n      return false;\n    }\n  }, [isConnected]);\n  \n  // Request data refresh\n  const requestRefresh = useCallback(() => {\n    return sendMessage('request_refresh', { timestamp: new Date().toISOString() });\n  }, [sendMessage]);\n  \n  // Initialize connection on mount\n  useEffect(() => {\n    if (options.autoConnect !== false) {\n      connect();\n    }\n    \n    // Cleanup on unmount\n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect, options.autoConnect]);\n  \n  // Heartbeat to maintain connection\n  useEffect(() => {\n    if (!isConnected) return;\n    \n    const heartbeatInterval = setInterval(() => {\n      if (socketRef.current) {\n        socketRef.current.emit('ping');\n      }\n    }, 30000); // 30 seconds\n    \n    return () => clearInterval(heartbeatInterval);\n  }, [isConnected]);\n  \n  return {\n    // Connection state\n    isConnected,\n    connectionError,\n    \n    // Data\n    strategyData,\n    priceUpdates,\n    lastMessage,\n    \n    // Actions\n    connect,\n    disconnect,\n    sendMessage,\n    requestRefresh,\n    \n    // Connection info\n    reconnectAttempts: reconnectAttempts.current,\n    maxReconnectAttempts\n  };\n};\n\nexport default useWebSocket;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,OAAOC,EAAE,MAAM,kBAAkB;AAEjC,MAAMC,YAAY,GAAGA,CAACC,GAAG,GAAG,uBAAuB,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EAAAC,EAAA;EACpE;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,eAAe,EAAEC,kBAAkB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAMmB,SAAS,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkB,mBAAmB,GAAGlB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMmB,iBAAiB,GAAGnB,MAAM,CAAC,CAAC,CAAC;;EAEnC;EACA,MAAMoB,oBAAoB,GAAGf,OAAO,CAACe,oBAAoB,IAAI,CAAC;EAC9D,MAAMC,iBAAiB,GAAGhB,OAAO,CAACgB,iBAAiB,IAAI,IAAI;;EAE3D;EACA,MAAMC,OAAO,GAAGrB,WAAW,CAAC,MAAM;IAChC,IAAI;MACFsB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEpB,GAAG,CAAC;MAE/Ca,SAAS,CAACQ,OAAO,GAAGvB,EAAE,CAACE,GAAG,EAAE;QAC1BsB,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,IAAI;QACd,GAAGvB,OAAO,CAACwB;MACb,CAAC,CAAC;;MAEF;MACAZ,SAAS,CAACQ,OAAO,CAACK,EAAE,CAAC,SAAS,EAAE,MAAM;QACpCP,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpChB,cAAc,CAAC,IAAI,CAAC;QACpBE,kBAAkB,CAAC,IAAI,CAAC;QACxBS,iBAAiB,CAACM,OAAO,GAAG,CAAC;;QAE7B;QACAR,SAAS,CAACQ,OAAO,CAACM,IAAI,CAAC,sBAAsB,EAAE;UAC7CC,KAAK,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,QAAQ;QACpD,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFf,SAAS,CAACQ,OAAO,CAACK,EAAE,CAAC,YAAY,EAAGG,MAAM,IAAK;QAC7CV,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAES,MAAM,CAAC;QAChDzB,cAAc,CAAC,KAAK,CAAC;;QAErB;QACA,IAAIyB,MAAM,KAAK,sBAAsB,EAAE;UACrCC,iBAAiB,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;MAEFjB,SAAS,CAACQ,OAAO,CAACK,EAAE,CAAC,eAAe,EAAGK,KAAK,IAAK;QAC/CZ,OAAO,CAACY,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDzB,kBAAkB,CAACyB,KAAK,CAACC,OAAO,CAAC;QACjC5B,cAAc,CAAC,KAAK,CAAC;QACrB0B,iBAAiB,CAAC,CAAC;MACrB,CAAC,CAAC;;MAEF;MACAjB,SAAS,CAACQ,OAAO,CAACK,EAAE,CAAC,eAAe,EAAGO,IAAI,IAAK;QAC9Cd,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEa,IAAI,CAAC;QAC/CvB,eAAe,CAACuB,IAAI,CAAC;QACrBzB,cAAc,CAAC;UACb0B,IAAI,EAAE,eAAe;UACrBD,IAAI;UACJE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFxB,SAAS,CAACQ,OAAO,CAACK,EAAE,CAAC,eAAe,EAAGO,IAAI,IAAK;QAC9Cd,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEa,IAAI,CAAC;QAC/CrB,eAAe,CAAC0B,WAAW,KAAK;UAC9B,GAAGA,WAAW;UACd,GAAGL;QACL,CAAC,CAAC,CAAC;QACHzB,cAAc,CAAC;UACb0B,IAAI,EAAE,eAAe;UACrBD,IAAI;UACJE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFxB,SAAS,CAACQ,OAAO,CAACK,EAAE,CAAC,OAAO,EAAGO,IAAI,IAAK;QACtCd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEa,IAAI,CAAC;QACvCzB,cAAc,CAAC;UACb0B,IAAI,EAAE,OAAO;UACbD,IAAI;UACJE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAON,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DzB,kBAAkB,CAACyB,KAAK,CAACC,OAAO,CAAC;IACnC;EACF,CAAC,EAAE,CAAChC,GAAG,EAAEC,OAAO,CAACwB,aAAa,CAAC,CAAC;;EAEhC;EACA,MAAMK,iBAAiB,GAAGjC,WAAW,CAAC,MAAM;IAC1C,IAAIkB,iBAAiB,CAACM,OAAO,GAAGL,oBAAoB,EAAE;MACpDD,iBAAiB,CAACM,OAAO,IAAI,CAAC;MAC9BF,OAAO,CAACC,GAAG,CAAC,sCAAsCL,iBAAiB,CAACM,OAAO,IAAIL,oBAAoB,EAAE,CAAC;MAEtGF,mBAAmB,CAACO,OAAO,GAAGkB,UAAU,CAAC,MAAM;QAC7CrB,OAAO,CAAC,CAAC;MACX,CAAC,EAAED,iBAAiB,GAAGF,iBAAiB,CAACM,OAAO,CAAC;IACnD,CAAC,MAAM;MACLF,OAAO,CAACY,KAAK,CAAC,qCAAqC,CAAC;MACpDzB,kBAAkB,CAAC,6CAA6C,CAAC;IACnE;EACF,CAAC,EAAE,CAACY,OAAO,EAAEF,oBAAoB,EAAEC,iBAAiB,CAAC,CAAC;;EAEtD;EACA,MAAMuB,UAAU,GAAG3C,WAAW,CAAC,MAAM;IACnC,IAAIiB,mBAAmB,CAACO,OAAO,EAAE;MAC/BoB,YAAY,CAAC3B,mBAAmB,CAACO,OAAO,CAAC;IAC3C;IAEA,IAAIR,SAAS,CAACQ,OAAO,EAAE;MACrBR,SAAS,CAACQ,OAAO,CAACmB,UAAU,CAAC,CAAC;MAC9B3B,SAAS,CAACQ,OAAO,GAAG,IAAI;IAC1B;IAEAjB,cAAc,CAAC,KAAK,CAAC;IACrBW,iBAAiB,CAACM,OAAO,GAAG,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqB,WAAW,GAAG7C,WAAW,CAAC,CAAC8C,KAAK,EAAEV,IAAI,KAAK;IAC/C,IAAIpB,SAAS,CAACQ,OAAO,IAAIlB,WAAW,EAAE;MACpCU,SAAS,CAACQ,OAAO,CAACM,IAAI,CAACgB,KAAK,EAAEV,IAAI,CAAC;MACnC,OAAO,IAAI;IACb,CAAC,MAAM;MACLd,OAAO,CAACyB,IAAI,CAAC,8CAA8C,CAAC;MAC5D,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACzC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM0C,cAAc,GAAGhD,WAAW,CAAC,MAAM;IACvC,OAAO6C,WAAW,CAAC,iBAAiB,EAAE;MAAEP,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;EAChF,CAAC,EAAE,CAACK,WAAW,CAAC,CAAC;;EAEjB;EACA/C,SAAS,CAAC,MAAM;IACd,IAAIM,OAAO,CAAC6C,WAAW,KAAK,KAAK,EAAE;MACjC5B,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,OAAO,MAAM;MACXsB,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACtB,OAAO,EAAEsB,UAAU,EAAEvC,OAAO,CAAC6C,WAAW,CAAC,CAAC;;EAE9C;EACAnD,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,WAAW,EAAE;IAElB,MAAM4C,iBAAiB,GAAGC,WAAW,CAAC,MAAM;MAC1C,IAAInC,SAAS,CAACQ,OAAO,EAAE;QACrBR,SAAS,CAACQ,OAAO,CAACM,IAAI,CAAC,MAAM,CAAC;MAChC;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMsB,aAAa,CAACF,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAAC5C,WAAW,CAAC,CAAC;EAEjB,OAAO;IACL;IACAA,WAAW;IACXE,eAAe;IAEf;IACAI,YAAY;IACZE,YAAY;IACZJ,WAAW;IAEX;IACAW,OAAO;IACPsB,UAAU;IACVE,WAAW;IACXG,cAAc;IAEd;IACA9B,iBAAiB,EAAEA,iBAAiB,CAACM,OAAO;IAC5CL;EACF,CAAC;AACH,CAAC;AAACd,EAAA,CA7LIH,YAAY;AA+LlB,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}