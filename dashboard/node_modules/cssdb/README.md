# cssdb [<img src="https://cssdb.org/images/cssdb.svg" alt="cssdb logo" width="90" height="90" align="right">][cssdb]

[![NPM Version][npm-img]][npm-url]
[![Build Status][cli-img]][cli-url]

[cssdb] is a comprehensive list of CSS features and their positions in
the process of becoming implemented web standards.

---

Did you come here to update the status of a CSS feature or add a new one?
Quick, read [CONTRIBUTING.md](CONTRIBUTING.md).

Did you come here to learn about the stages? Quick, read [STAGES.md](STAGES.md).

---

[cssdb] ranks CSS features by stages that reflect the real-life stability of
new CSS features.

You can read an [inside view of the CSSWG] to learn about the official
(and unofficial) development stages of CSS specifications. In reality,
specifications and browser implementations happen out of sync. For example,
there are stable CSS features missing in all browsers, while other CSS features
developed outside the [CSSWG] have appeared in browsers behind flags. This is
too ambiguous for the web development community, and a more accountable process
is desired.

[cli-img]: https://github.com/csstools/cssdb/actions/workflows/test.yml/badge.svg
[cli-url]: https://github.com/csstools/cssdb/actions/workflows/test.yml
[cssdb]: https://github.com/csstools/cssdb
[CSSWG]: https://wiki.csswg.org/spec
[inside view of the CSSWG]: https://fantasai.inkedblade.net/weblog/2011/inside-csswg/process
[npm-img]: https://img.shields.io/npm/v/cssdb.svg
[npm-url]: https://www.npmjs.com/package/cssdb
