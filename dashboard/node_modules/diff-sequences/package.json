{"name": "diff-sequences", "version": "27.5.1", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/diff-sequences"}, "license": "MIT", "description": "Compare items in two sequences to find a longest common subsequence", "keywords": ["fast", "linear", "space", "callback", "diff"], "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "scripts": {"perf": "node --expose-gc perf/index.js"}, "devDependencies": {"benchmark": "^2.1.4", "diff": "^5.0.0", "fast-check": "^2.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}