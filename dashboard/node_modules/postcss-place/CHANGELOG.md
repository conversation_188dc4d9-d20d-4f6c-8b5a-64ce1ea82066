# Changes to PostCSS Place Properties

### 7.0.5 (July 8, 2022)

- Fix case insensitive matching.

### 7.0.4 (February 5, 2022)

- Improved `es module` and `commonjs` compatibility

### 7.0.3 (January 2, 2022)

- Removed Sourcemaps from package tarball.
- Moved CLI to CLI Package. See [announcement](https://github.com/csstools/postcss-plugins/discussions/121).

### 7.0.2 (December 13, 2021)

- Changed: now uses `postcss-value-parser` for parsing.
- Updated: documentation

## 7.0.1 (November 18, 2021)

- Added: Safeguards against postcss-values-parser potentially throwing an error.
- Updated: postcss-value-parser to 6.0.1 (patch)

## 7.0.0 (September 17, 2021)

- Updated: Support for PostCS 8+ (major).
- Updated: Support for Node 12+ (major).

## 6.0.0 (January 12, 2021)

- Updated: Support for PostCSS v8+
- Updated: PostCSS Values Parser to 4.x

## 5.0.0 (April 26, 2020)

- Updated: PostCSS to 7.0.27 (patch)
- Updated: PostCSS Values Parser to 3.2.1 (major)
- Updated: Support for Node v10+

### 4.0.1 (September 18, 2018)

- Updated: PostCSS Values Parser 2 (patch for this project)

### 4.0.0 (September 17, 2018)

- Updated: Support for PostCSS v7+
- Updated: Support for Node v6+

### 3.0.1 (May 8, 2018)

- Updated: `postcss-values-parser` to v1.5.0 (major)
- Updated: `postcss` to v6.0.22 (patch)

### 2.0.0 (June 30, 2017)

- Added: Node 4+ compatibility
- Added: PostCSS 6+ compatibility

### 1.0.2 (December 8, 2016)

- Updated: Use destructing assignment on plugin options
- Updated: Use template literals

### 1.0.1 (December 6, 2016)

- Updated: boilerplate conventions (`postcss-tape`)

### 1.0.0 (November 25, 2016)

- Initial version
