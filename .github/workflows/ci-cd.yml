name: Momentum Strategy CI/CD Pipeline

# Comprehensive CI/CD pipeline for momentum trading strategy
# Includes testing, linting, security checks, and deployment
# Author: <PERSON><PERSON><PERSON><PERSON>

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run daily at 2 AM UTC for scheduled testing
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '18'
  
jobs:
  # Python Backend Testing
  python-tests:
    name: Python Backend Tests
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov flake8 black isort bandit safety
        
    - name: Code formatting check (Black)
      run: |
        black --check momentum_strategy/ tests/
        
    - name: Import sorting check (isort)
      run: |
        isort --check-only momentum_strategy/ tests/
        
    - name: Linting (flake8)
      run: |
        flake8 momentum_strategy/ tests/ --max-line-length=100 --ignore=E203,W503
        
    - name: Security check (Bandit)
      run: |
        bandit -r momentum_strategy/ -f json -o bandit-report.json || true
        
    - name: Dependency vulnerability check (Safety)
      run: |
        safety check --json --output safety-report.json || true
        
    - name: Run unit tests with coverage
      run: |
        cd momentum_strategy
        python -m pytest ../tests/ -v --cov=. --cov-report=xml --cov-report=html
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./momentum_strategy/coverage.xml
        flags: python
        name: python-coverage
        
    - name: Upload test artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: python-test-results-${{ matrix.python-version }}
        path: |
          momentum_strategy/htmlcov/
          bandit-report.json
          safety-report.json

  # React Frontend Testing
  react-tests:
    name: React Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: dashboard/package-lock.json
        
    - name: Install dependencies
      run: |
        cd dashboard
        npm ci
        
    - name: Linting (ESLint)
      run: |
        cd dashboard
        npm run lint || true
        
    - name: Type checking (if TypeScript)
      run: |
        cd dashboard
        npm run type-check || true
        
    - name: Run tests with coverage
      run: |
        cd dashboard
        npm test -- --coverage --watchAll=false --testResultsProcessor=jest-sonar-reporter
        
    - name: Build application
      run: |
        cd dashboard
        npm run build
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./dashboard/coverage/lcov.info
        flags: react
        name: react-coverage
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: react-build
        path: dashboard/build/

  # Integration Testing
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [python-tests, react-tests]
    
    services:
      redis:
        image: redis:alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest requests
        
    - name: Install Node dependencies
      run: |
        cd dashboard
        npm ci
        
    - name: Start backend services
      run: |
        cd momentum_strategy
        python simple_api.py &
        sleep 10
        
    - name: Start frontend
      run: |
        cd dashboard
        npm start &
        sleep 30
        
    - name: Run integration tests
      run: |
        python -m pytest tests/integration/ -v
        
    - name: API health check
      run: |
        curl -f http://localhost:9000/api/health || exit 1
        
    - name: Frontend health check
      run: |
        curl -f http://localhost:3000 || exit 1

  # Security and Quality Checks
  security-quality:
    name: Security & Quality Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
        
    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Performance Testing
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [python-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark memory-profiler
        
    - name: Run performance benchmarks
      run: |
        cd momentum_strategy
        python -m pytest ../tests/performance/ -v --benchmark-json=benchmark.json
        
    - name: Memory profiling
      run: |
        cd momentum_strategy
        python -m memory_profiler main.py --universe global_equity
        
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          momentum_strategy/benchmark.json
          momentum_strategy/*.dat

  # Documentation Generation
  documentation:
    name: Generate Documentation
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install documentation dependencies
      run: |
        python -m pip install --upgrade pip
        pip install sphinx sphinx-rtd-theme sphinx-autodoc-typehints
        pip install -r requirements.txt
        
    - name: Generate API documentation
      run: |
        cd docs
        sphinx-apidoc -o source/ ../momentum_strategy/
        make html
        
    - name: Deploy documentation to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/build/html

  # Deployment
  deploy:
    name: Deploy Application
    runs-on: ubuntu-latest
    needs: [python-tests, react-tests, integration-tests, security-quality]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    environment:
      name: production
      url: https://momentum-strategy.example.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: react-build
        path: dashboard/build/
        
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your deployment commands here
        
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # Add smoke test commands here
        
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add production deployment commands here
        
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # Cleanup
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [deploy]
    if: always()
    
    steps:
    - name: Clean up artifacts
      run: |
        echo "Cleaning up temporary artifacts..."
        
    - name: Update deployment status
      run: |
        echo "Pipeline completed successfully!"
        echo "Author: Rishabh Ashok Patil"
        echo "Timestamp: $(date)"
