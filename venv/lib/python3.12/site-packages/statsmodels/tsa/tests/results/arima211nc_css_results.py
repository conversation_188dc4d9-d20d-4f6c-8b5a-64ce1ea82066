import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-240.21658671417])

nobs = np.array([202])

k = np.array([4])

k_exog = np.array([1])

sigma = np.array([.79473430527544])

chi2 = np.array([54633.096432541])

df_model = np.array([3])

k_ar = np.array([2])

k_ma = np.array([1])

params = np.array([
    1.1970355174119,
    -.19724105359909,
    -.91770441432171,
    .63160261598163])

cov_params = np.array([
    .00182362158934,
    -.00163271308366,
    -.00140915922719,
    -.00044400541718,
    -.00163271308366,
    .00148731108625,
    .00117094734518,
    .00044939207189,
    -.00140915922719,
    .00117094734518,
    .00181742086472,
    .00032737864417,
    -.00044400541718,
    .00044939207189,
    .00032737864417,
    .00071193796554]).reshape(4, 4)

xb = np.array([
    0,
    0,
    .04999904707074,
    .06866559386253,
    .02903163060546,
    .07047952711582,
    .03383458778262,
    .08519082516432,
    .06387747824192,
    .0323903337121,
    .0664371997118,
    .056028008461,
    .05634552612901,
    .08741936087608,
    .04945361241698,
    .0881454795599,
    .06608437746763,
    .05997726693749,
    .1058451384306,
    .07246486097574,
    .10775856673717,
    .06419499218464,
    .07649331539869,
    .08432687073946,
    .10236189514399,
    .09031195193529,
    .11902847886086,
    .08933536708355,
    .13242828845978,
    .18790599703789,
    .1410321444273,
    .2076324224472,
    .12561346590519,
    .16128000617027,
    .19236192107201,
    .20115886628628,
    .23716603219509,
    .22255305945873,
    .28473255038261,
    .27441227436066,
    .28466689586639,
    .34994292259216,
    .3424659371376,
    .35532799363136,
    .39506548643112,
    .41180691123009,
    .37130555510521,
    .40151777863503,
    .40951976180077,
    .33306270837784,
    .40587121248245,
    .35764938592911,
    .35284259915352,
    .34843212366104,
    .34438464045525,
    .36860400438309,
    .3990384042263,
    .54691004753113,
    .44432625174522,
    .70020270347595,
    .70163971185684,
    .77033877372742,
    .78572767972946,
    .95923948287964,
    .90811860561371,
    .77250319719315,
    .85019183158875,
    .83438217639923,
    .83959633111954,
    .67678385972977,
    .81331378221512,
    .8202628493309,
    .79870623350143,
    .93831378221512,
    .8281461596489,
    .8256653547287,
    .9071888923645,
    .95076316595078,
    1.0827594995499,
    1.1249971389771,
    1.1078934669495,
    1.3271758556366,
    1.4741442203522,
    1.4939774274826,
    1.7192279100418,
    1.8355281352997,
    1.5873348712921,
    1.7079899311066,
    1.8515517711639,
    1.7368041276932,
    1.8895095586777,
    1.7913619279861,
    1.5485136508942,
    1.3914349079132,
    1.8569093942642,
    1.378589630127,
    1.0909280776978,
    1.2919955253601,
    1.2874838113785,
    1.2636196613312,
    1.3255174160004,
    1.2952193021774,
    1.1754019260406,
    1.2002106904984,
    1.0717958211899,
    1.2283787727356,
    1.0664055347443,
    1.0640426874161,
    1.2097471952438,
    .49885395169258,
    .91795635223389,
    .88015007972717,
    1.0048481225967,
    1.0485925674438,
    1.0131514072418,
    1.0480036735535,
    1.0044000148773,
    1.0596977472305,
    1.0989319086075,
    1.1431447267532,
    1.1360602378845,
    1.316884636879,
    1.2248164415359,
    1.0992801189423,
    1.4178918600082,
    1.2780615091324,
    1.3436778783798,
    1.727570772171,
    1.376532793045,
    1.1185711622238,
    1.2548811435699,
    1.2139776945114,
    1.22409760952,
    1.2136551141739,
    1.2040791511536,
    1.2232189178467,
    1.1931306123734,
    1.1573059558868,
    1.0603547096252,
    1.1422899961472,
    1.0268490314484,
    1.0556720495224,
    1.1264756917953,
    1.0764141082764,
    1.0978548526764,
    1.1536711454391,
    1.025780916214,
    1.034966468811,
    1.1074740886688,
    1.1707112789154,
    1.0496238470078,
    1.1209251880646,
    1.1271858215332,
    .93740028142929,
    .90130144357681,
    1.0357736349106,
    .87323325872421,
    .75861483812332,
    .93606770038605,
    .85732334852219,
    .87216699123383,
    .97779452800751,
    .88410341739655,
    1.0446182489395,
    1.0177079439163,
    1.144193649292,
    1.2372444868088,
    1.1155867576599,
    1.2619564533234,
    1.0462523698807,
    1.0816910266876,
    .85130125284195,
    .76972281932831,
    1.1335872411728,
    .92024201154709,
    1.0416384935379,
    1.1102936267853,
    .91037821769714,
    .85678082704544,
    1.022847533226,
    1.0930491685867,
    1.0342184305191,
    1.2070096731186,
    1.2472279071808,
    1.0886085033417,
    1.3604420423508,
    1.1053978204727,
    2.0939025878906,
    1.0898643732071,
    1.3238569498062,
    1.5171576738358,
    .77435439825058,
    1.3360253572464,
    1.5512014627457,
    1.3569095134735,
    1.4669530391693,
    1.9312930107117,
    1.52878677845,
    2.3952746391296,
    .80755305290222,
    -.2365039139986,
    .85178333520889,
    1.1858888864517])

y = np.array([
    np.nan,
    28.979999542236,
    29.199998855591,
    29.4186668396,
    29.399032592773,
    29.610481262207,
    29.583833694458,
    29.835191726685,
    29.903877258301,
    29.842390060425,
    29.986436843872,
    30.036027908325,
    30.096345901489,
    30.29741859436,
    30.269453048706,
    30.468145370483,
    30.506084442139,
    30.539976119995,
    30.795845031738,
    30.822463989258,
    31.047760009766,
    31.014196395874,
    31.096494674683,
    31.204328536987,
    31.382362365723,
    31.470310211182,
    31.699028015137,
    31.739334106445,
    32.012428283691,
    32.467903137207,
    32.591033935547,
    33.057632446289,
    33.025615692139,
    33.261280059814,
    33.592365264893,
    33.901161193848,
    34.33716583252,
    34.622554779053,
    35.184734344482,
    35.574413299561,
    35.984668731689,
    36.649940490723,
    37.142463684082,
    37.655326843262,
    38.295066833496,
    38.911808013916,
    39.271308898926,
    39.801517486572,
    40.309520721436,
    40.433059692383,
    41.005870819092,
    41.257652282715,
    41.552845001221,
    41.848430633545,
    42.144382476807,
    42.568603515625,
    43.099040985107,
    44.246910095215,
    44.644325256348,
    46.300201416016,
    47.501640319824,
    48.870338439941,
    50.08572769165,
    51.959239959717,
    53.208118438721,
    53.77250289917,
    54.850193023682,
    55.734382629395,
    56.639595031738,
    56.776782989502,
    57.813312530518,
    58.720264434814,
    59.498706817627,
    60.938312530518,
    61.628147125244,
    62.425662994385,
    63.607189178467,
    64.850761413574,
    66.58275604248,
    68.224998474121,
    69.607894897461,
    71.927177429199,
    74.474143981934,
    76.693977355957,
    79.719230651855,
    82.735527038574,
    84.18733215332,
    86.407989501953,
    89.051551818848,
    90.836799621582,
    93.389511108398,
    95.191360473633,
    95.948516845703,
    96.39143371582,
    99.356910705566,
    99.478584289551,
    98.990928649902,
    100.09199523926,
    101.08748626709,
    102.063621521,
    103.42551422119,
    104.59522247314,
    105.27539825439,
    106.30020904541,
    106.77178955078,
    108.2283782959,
    108.76640319824,
    109.5640411377,
    111.10974884033,
    109.19885253906,
    110.41795349121,
    111.08014678955,
    112.40484619141,
    113.74858856201,
    114.81315612793,
    116.04800415039,
    117.00440216064,
    118.25969696045,
    119.59893035889,
    121.04314422607,
    122.33605957031,
    124.41688537598,
    125.72481536865,
    126.49928283691,
    128.91789245605,
    130.17805480957,
    131.84367370605,
    135.12756347656,
    136.07652282715,
    136.21858215332,
    137.45487976074,
    138.41397094727,
    139.52409362793,
    140.61364746094,
    141.70408630371,
    142.92321777344,
    143.99313354492,
    144.9573059082,
    145.56034851074,
    146.74229431152,
    147.32685852051,
    148.25567626953,
    149.52647399902,
    150.47640991211,
    151.59785461426,
    152.95367431641,
    153.62579345703,
    154.53497314453,
    155.80746459961,
    157.27072143555,
    158.04962158203,
    159.32092285156,
    160.52717590332,
    160.83738708496,
    161.30130004883,
    162.53576660156,
    162.87322998047,
    162.95861816406,
    164.13606262207,
    164.75732421875,
    165.57215881348,
    166.8777923584,
    167.58410644531,
    169.14462280273,
    170.31771850586,
    172.04418945313,
    173.93724060059,
    175.01557922363,
    176.86196899414,
    177.44624328613,
    178.48168945313,
    178.4513092041,
    178.4697265625,
    180.43359375,
    180.92024230957,
    182.24163818359,
    183.71029663086,
    184.11038208008,
    184.5567779541,
    185.92283630371,
    187.39305114746,
    188.43421936035,
    190.30702209473,
    192.04722595215,
    192.88861083984,
    195.16044616699,
    195.8053894043,
    201.29389953613,
    200.48985290527,
    202.0238494873,
    204.21714782715,
    202.67434692383,
    204.91003417969,
    207.47120666504,
    208.6949005127,
    210.59994506836,
    214.42628479004,
    215.52578735352,
    221.00527954102,
    217.6965637207,
    211.93748474121,
    213.52278137207,
    215.65487670898])

resid = np.array([
    np.nan,
    .17000007629395,
    .150001719594,
    -.04866513609886,
    .1409684419632,
    -.06048120185733,
    .16616617143154,
    .00480932369828,
    -.09387816488743,
    .07761027663946,
    -.00643773004413,
    .00397336483002,
    .1136526465416,
    -.07741913199425,
    .11054623872042,
    -.02814410813153,
    -.02608536928892,
    .15002372860909,
    -.04584567248821,
    .11753567308187,
    -.09775833785534,
    .00580469891429,
    .02350706420839,
    .07567297667265,
    -.00236342288554,
    .10968881100416,
    -.04902878031135,
    .14066417515278,
    .2675713300705,
    -.01790400780737,
    .25896555185318,
    -.15762937068939,
    .074383482337,
    .13872304558754,
    .10763731598854,
    .19883884489536,
    .06283701956272,
    .27744692564011,
    .11526516824961,
    .12558923661709,
    .3153315782547,
    .15005706250668,
    .1575340628624,
    .24467428028584,
    .20493300259113,
    -.01180539745837,
    .12869445979595,
    .09848223626614,
    -.20952282845974,
    .166937276721,
    -.1058681756258,
    -.05765015259385,
    -.05284334719181,
    -.04843288660049,
    .05561690032482,
    .13139598071575,
    .60096162557602,
    -.04691004380584,
    .95567148923874,
    .49979802966118,
    .5983595252037,
    .42966198921204,
    .91427308320999,
    .34075975418091,
    -.20811785757542,
    .22749677300453,
    .049809679389,
    .06561553478241,
    -.53959709405899,
    .2232176810503,
    .08668774366379,
    -.02026358619332,
    .50129300355911,
    -.13831453025341,
    -.02814690209925,
    .27433693408966,
    .29281187057495,
    .64923530817032,
    .51723897457123,
    .27500438690186,
    .99210506677628,
    1.0728256702423,
    .72585266828537,
    1.3060256242752,
    1.1807736158371,
    -.13553117215633,
    .51266354322433,
    .79201000928879,
    .0484497398138,
    .66319739818573,
    .0104919327423,
    -.79136198759079,
    -.94851511716843,
    1.1085650920868,
    -1.2569109201431,
    -1.5785865783691,
    -.19092650711536,
    -.29199549555779,
    -.2874838411808,
    .03637577593327,
    -.12551285326481,
    -.49522390961647,
    -.17540194094181,
    -.60021221637726,
    .22820720076561,
    -.52838176488876,
    -.26640248298645,
    .33595886826515,
    -2.4097516536713,
    .30114910006523,
    -.21795941889286,
    .31985449790955,
    .29514732956886,
    .05141358822584,
    .18684551119804,
    -.04800364747643,
    .19559693336487,
    .24030530452728,
    .30106961727142,
    .15685074031353,
    .76394122838974,
    .08311692625284,
    -.32481494545937,
    1.0007183551788,
    -.01789797656238,
    .32194453477859,
    1.5563160181046,
    -.42756772041321,
    -.97652357816696,
    -.01858037337661,
    -.25488117337227,
    -.11397163569927,
    -.12410674989223,
    -.1136489585042,
    -.00408222479746,
    -.12321277707815,
    -.19313062727451,
    -.45730903744698,
    .03965143114328,
    -.44229298830032,
    -.12685517966747,
    .1443248540163,
    -.12647566199303,
    .02359197475016,
    .20214818418026,
    -.35366812348366,
    -.12578700482845,
    .16503044962883,
    .29253509640694,
    -.27071738243103,
    .15037304162979,
    .07907173037529,
    -.6271858215332,
    -.43740031123161,
    .19870468974113,
    -.53577369451523,
    -.67323631048203,
    .24138513207436,
    -.23607075214386,
    -.05732027813792,
    .32782995700836,
    -.17779149115086,
    .51590573787689,
    .15537866950035,
    .5822828412056,
    .65580940246582,
    -.03724759072065,
    .58442544937134,
    -.46196871995926,
    -.04625232890248,
    -.88167881965637,
    -.75131040811539,
    .83028328418732,
    -.43359026312828,
    .2797549366951,
    .35837066173553,
    -.51030284166336,
    -.41037824749947,
    .34321609139442,
    .37716165184975,
    .00694172456861,
    .66579383611679,
    .49298724532127,
    -.24722795188427,
    .91139149665833,
    -.46044808626175,
    3.3946022987366,
    -1.8939057588577,
    .21013870835304,
    .67614299058914,
    -2.3171606063843,
    .89965683221817,
    1.0099676847458,
    -.13320215046406,
    .43808862566948,
    1.8950464725494,
    -.42929407954216,
    3.0842199325562,
    -4.1162676811218,
    -5.5225644111633,
    .73351317644119,
    .94620555639267,
    .73011147975922])

yr = np.array([
    np.nan,
    .17000007629395,
    .150001719594,
    -.04866513609886,
    .1409684419632,
    -.06048120185733,
    .16616617143154,
    .00480932369828,
    -.09387816488743,
    .07761027663946,
    -.00643773004413,
    .00397336483002,
    .1136526465416,
    -.07741913199425,
    .11054623872042,
    -.02814410813153,
    -.02608536928892,
    .15002372860909,
    -.04584567248821,
    .11753567308187,
    -.09775833785534,
    .00580469891429,
    .02350706420839,
    .07567297667265,
    -.00236342288554,
    .10968881100416,
    -.04902878031135,
    .14066417515278,
    .2675713300705,
    -.01790400780737,
    .25896555185318,
    -.15762937068939,
    .074383482337,
    .13872304558754,
    .10763731598854,
    .19883884489536,
    .06283701956272,
    .27744692564011,
    .11526516824961,
    .12558923661709,
    .3153315782547,
    .15005706250668,
    .1575340628624,
    .24467428028584,
    .20493300259113,
    -.01180539745837,
    .12869445979595,
    .09848223626614,
    -.20952282845974,
    .166937276721,
    -.1058681756258,
    -.05765015259385,
    -.05284334719181,
    -.04843288660049,
    .05561690032482,
    .13139598071575,
    .60096162557602,
    -.04691004380584,
    .95567148923874,
    .49979802966118,
    .5983595252037,
    .42966198921204,
    .91427308320999,
    .34075975418091,
    -.20811785757542,
    .22749677300453,
    .049809679389,
    .06561553478241,
    -.53959709405899,
    .2232176810503,
    .08668774366379,
    -.02026358619332,
    .50129300355911,
    -.13831453025341,
    -.02814690209925,
    .27433693408966,
    .29281187057495,
    .64923530817032,
    .51723897457123,
    .27500438690186,
    .99210506677628,
    1.0728256702423,
    .72585266828537,
    1.3060256242752,
    1.1807736158371,
    -.13553117215633,
    .51266354322433,
    .79201000928879,
    .0484497398138,
    .66319739818573,
    .0104919327423,
    -.79136198759079,
    -.94851511716843,
    1.1085650920868,
    -1.2569109201431,
    -1.5785865783691,
    -.19092650711536,
    -.29199549555779,
    -.2874838411808,
    .03637577593327,
    -.12551285326481,
    -.49522390961647,
    -.17540194094181,
    -.60021221637726,
    .22820720076561,
    -.52838176488876,
    -.26640248298645,
    .33595886826515,
    -2.4097516536713,
    .30114910006523,
    -.21795941889286,
    .31985449790955,
    .29514732956886,
    .05141358822584,
    .18684551119804,
    -.04800364747643,
    .19559693336487,
    .24030530452728,
    .30106961727142,
    .15685074031353,
    .76394122838974,
    .08311692625284,
    -.32481494545937,
    1.0007183551788,
    -.01789797656238,
    .32194453477859,
    1.5563160181046,
    -.42756772041321,
    -.97652357816696,
    -.01858037337661,
    -.25488117337227,
    -.11397163569927,
    -.12410674989223,
    -.1136489585042,
    -.00408222479746,
    -.12321277707815,
    -.19313062727451,
    -.45730903744698,
    .03965143114328,
    -.44229298830032,
    -.12685517966747,
    .1443248540163,
    -.12647566199303,
    .02359197475016,
    .20214818418026,
    -.35366812348366,
    -.12578700482845,
    .16503044962883,
    .29253509640694,
    -.27071738243103,
    .15037304162979,
    .07907173037529,
    -.6271858215332,
    -.43740031123161,
    .19870468974113,
    -.53577369451523,
    -.67323631048203,
    .24138513207436,
    -.23607075214386,
    -.05732027813792,
    .32782995700836,
    -.17779149115086,
    .51590573787689,
    .15537866950035,
    .5822828412056,
    .65580940246582,
    -.03724759072065,
    .58442544937134,
    -.46196871995926,
    -.04625232890248,
    -.88167881965637,
    -.75131040811539,
    .83028328418732,
    -.43359026312828,
    .2797549366951,
    .35837066173553,
    -.51030284166336,
    -.41037824749947,
    .34321609139442,
    .37716165184975,
    .00694172456861,
    .66579383611679,
    .49298724532127,
    -.24722795188427,
    .91139149665833,
    -.46044808626175,
    3.3946022987366,
    -1.8939057588577,
    .21013870835304,
    .67614299058914,
    -2.3171606063843,
    .89965683221817,
    1.0099676847458,
    -.13320215046406,
    .43808862566948,
    1.8950464725494,
    -.42929407954216,
    3.0842199325562,
    -4.1162676811218,
    -5.5225644111633,
    .73351317644119,
    .94620555639267,
    .73011147975922])

mse = np.array([
    1.1635265350342,
    .70545583963394,
    .63365471363068,
    .633325278759,
    .63304948806763,
    .63281834125519,
    .63262450695038,
    .63246184587479,
    .63232523202896,
    .63221049308777,
    .63211411237717,
    .63203299045563,
    .63196486234665,
    .63190752267838,
    .63185924291611,
    .63181865215302,
    .63178449869156,
    .63175576925278,
    .631731569767,
    .63171118497849,
    .63169401884079,
    .63167959451675,
    .63166743516922,
    .63165718317032,
    .63164860010147,
    .63164132833481,
    .6316351890564,
    .63163006305695,
    .63162571191788,
    .63162207603455,
    .63161903619766,
    .63161641359329,
    .63161426782608,
    .63161242008209,
    .63161087036133,
    .63160955905914,
    .63160848617554,
    .63160753250122,
    .63160675764084,
    .63160610198975,
    .63160556554794,
    .63160508871078,
    .63160473108292,
    .63160437345505,
    .63160407543182,
    .63160383701324,
    .63160365819931,
    .63160347938538,
    .63160336017609,
    .6316032409668,
    .63160312175751,
    .63160306215286,
    .63160300254822,
    .63160294294357,
    .63160288333893,
    .63160282373428,
    .63160282373428,
    .63160276412964,
    .63160276412964,
    .63160270452499,
    .63160270452499,
    .63160270452499,
    .63160270452499,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035,
    .63160264492035])

stdp = np.array([
    0,
    0,
    .04999904707074,
    .06866559386253,
    .02903163060546,
    .07047952711582,
    .03383458778262,
    .08519082516432,
    .06387747824192,
    .0323903337121,
    .0664371997118,
    .056028008461,
    .05634552612901,
    .08741936087608,
    .04945361241698,
    .0881454795599,
    .06608437746763,
    .05997726693749,
    .1058451384306,
    .07246486097574,
    .10775856673717,
    .06419499218464,
    .07649331539869,
    .08432687073946,
    .10236189514399,
    .09031195193529,
    .11902847886086,
    .08933536708355,
    .13242828845978,
    .18790599703789,
    .1410321444273,
    .2076324224472,
    .12561346590519,
    .16128000617027,
    .19236192107201,
    .20115886628628,
    .23716603219509,
    .22255305945873,
    .28473255038261,
    .27441227436066,
    .28466689586639,
    .34994292259216,
    .3424659371376,
    .35532799363136,
    .39506548643112,
    .41180691123009,
    .37130555510521,
    .40151777863503,
    .40951976180077,
    .33306270837784,
    .40587121248245,
    .35764938592911,
    .35284259915352,
    .34843212366104,
    .34438464045525,
    .36860400438309,
    .3990384042263,
    .54691004753113,
    .44432625174522,
    .70020270347595,
    .70163971185684,
    .77033877372742,
    .78572767972946,
    .95923948287964,
    .90811860561371,
    .77250319719315,
    .85019183158875,
    .83438217639923,
    .83959633111954,
    .67678385972977,
    .81331378221512,
    .8202628493309,
    .79870623350143,
    .93831378221512,
    .8281461596489,
    .8256653547287,
    .9071888923645,
    .95076316595078,
    1.0827594995499,
    1.1249971389771,
    1.1078934669495,
    1.3271758556366,
    1.4741442203522,
    1.4939774274826,
    1.7192279100418,
    1.8355281352997,
    1.5873348712921,
    1.7079899311066,
    1.8515517711639,
    1.7368041276932,
    1.8895095586777,
    1.7913619279861,
    1.5485136508942,
    1.3914349079132,
    1.8569093942642,
    1.378589630127,
    1.0909280776978,
    1.2919955253601,
    1.2874838113785,
    1.2636196613312,
    1.3255174160004,
    1.2952193021774,
    1.1754019260406,
    1.2002106904984,
    1.0717958211899,
    1.2283787727356,
    1.0664055347443,
    1.0640426874161,
    1.2097471952438,
    .49885395169258,
    .91795635223389,
    .88015007972717,
    1.0048481225967,
    1.0485925674438,
    1.0131514072418,
    1.0480036735535,
    1.0044000148773,
    1.0596977472305,
    1.0989319086075,
    1.1431447267532,
    1.1360602378845,
    1.316884636879,
    1.2248164415359,
    1.0992801189423,
    1.4178918600082,
    1.2780615091324,
    1.3436778783798,
    1.727570772171,
    1.376532793045,
    1.1185711622238,
    1.2548811435699,
    1.2139776945114,
    1.22409760952,
    1.2136551141739,
    1.2040791511536,
    1.2232189178467,
    1.1931306123734,
    1.1573059558868,
    1.0603547096252,
    1.1422899961472,
    1.0268490314484,
    1.0556720495224,
    1.1264756917953,
    1.0764141082764,
    1.0978548526764,
    1.1536711454391,
    1.025780916214,
    1.034966468811,
    1.1074740886688,
    1.1707112789154,
    1.0496238470078,
    1.1209251880646,
    1.1271858215332,
    .93740028142929,
    .90130144357681,
    1.0357736349106,
    .87323325872421,
    .75861483812332,
    .93606770038605,
    .85732334852219,
    .87216699123383,
    .97779452800751,
    .88410341739655,
    1.0446182489395,
    1.0177079439163,
    1.144193649292,
    1.2372444868088,
    1.1155867576599,
    1.2619564533234,
    1.0462523698807,
    1.0816910266876,
    .85130125284195,
    .76972281932831,
    1.1335872411728,
    .92024201154709,
    1.0416384935379,
    1.1102936267853,
    .91037821769714,
    .85678082704544,
    1.022847533226,
    1.0930491685867,
    1.0342184305191,
    1.2070096731186,
    1.2472279071808,
    1.0886085033417,
    1.3604420423508,
    1.1053978204727,
    2.0939025878906,
    1.0898643732071,
    1.3238569498062,
    1.5171576738358,
    .77435439825058,
    1.3360253572464,
    1.5512014627457,
    1.3569095134735,
    1.4669530391693,
    1.9312930107117,
    1.52878677845,
    2.3952746391296,
    .80755305290222,
    -.2365039139986,
    .85178333520889,
    1.1858888864517])

icstats = np.array([
    202,
    np.nan,
    -240.21658671417,
    4,
    488.43317342834,
    501.66624421795])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    stdp=stdp,
    icstats=icstats
)
