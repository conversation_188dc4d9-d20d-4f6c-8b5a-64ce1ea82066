"""
ARMA autocorrelation results (from R's ARMAacf function)

These numbers are printed out when running the R script results/test_arma_acf.R
"""

bd_example_3_3_2 = [
    1.00000000000, 0.87500000000, 0.62500000000, 0.40625000000, 0.25000000000,
    0.14843750000, 0.08593750000, 0.04882812500, 0.02734375000, 0.01513671875]

custom_example_1 = [
    1.00000000000000000, 0.89043062200956924, 0.65657894736842093,
    0.43397129186602867, 0.26982655502392344, 0.16133373205741627,
    0.09387709330143541, 0.05354366028708134, 0.03007438696172249,
    0.01668847188995216]

custom_example_2 = [
    1.0000000000000000, 0.9092437831227068, 0.7142784345699144,
    0.5076054830819404, 0.3290358744394619, 0.2021345036689768,
    0.1198755350591113, 0.0693419091418671, 0.0393730253770893,
    0.0220375480916225]

custom_example_3 = [
    1.00000000000000000, 0.88441824705279337, 0.64345207585853414,
    0.39159405433111233, 0.20382175807278322, 0.10592324449000515,
    0.05496780497180934, 0.02848699384930806, 0.01474504260635572,
    0.00762329414402871]

custom_example_4 = [
    1.000000000000000000, 0.879686150302332326, 0.625215951626835631,
    0.359037575583069424, 0.159975165562913885, 0.062441513101065334,
    0.022447721710336863, 0.006837343435070529, 0.001225413007486314,
    -0.000483922851281319]

custom_example_5 = [
    1.000000000000000, 0.929315745090953, 0.784137453318877, 0.651808517046139,
    0.548705728225515, 0.464167344295868, 0.392171763944103, 0.331000500692688,
    0.279374294136249, 0.235841345357487]

custom_example_6 = [
    1.0000000000000000, 0.9136917896587146, 0.7322751541352182,
    0.5581676172376039, 0.4164680076696708, 0.3044690292908558,
    0.2195550313904376, 0.1571761939728106, 0.1119109386708032,
    0.0793489418521015]

custom_example_7 = [
    1.000000000000000, 0.918273770449316, 0.747774138591147, 0.587247490228175,
    0.460496808034306, 0.362548660813911, 0.287125976307581, 0.228131600167948,
    0.181325081575292, 0.144087282284054]
