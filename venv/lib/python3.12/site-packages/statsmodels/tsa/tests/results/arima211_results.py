import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-239.91961140777])

nobs = np.array([202])

k = np.array([5])

k_exog = np.array([1])

sigma = np.array([.79249918716869])

chi2 = np.array([907.36856676748])

df_model = np.array([3])

k_ar = np.array([2])

k_ma = np.array([1])

params = np.array([
    .86132870569126,
    1.1429974468925,
    -.16912378249242,
    -.87400688606796,
    .79249918716869])

cov_params = np.array([
    .22693223682292,
    -.00989623843683,
    -.00092349277863,
    .00706357033712,
    -.00195332705416,
    -.00989623843683,
    .00771565502349,
    -.00405286187622,
    -.00748374882497,
    -.00006513770699,
    -.00092349277863,
    -.00405286187622,
    .00252946620109,
    .00372566422545,
    .00024079697522,
    .00706357033712,
    -.00748374882497,
    .00372566422545,
    .00812286034649,
    8.202907726e-06,
    -.00195332705416,
    -.00006513770699,
    .00024079697522,
    8.202907726e-06,
    .00031052148013]).reshape(5, 5)

xb = np.array([
    .86132872104645,
    .86132872104645,
    .58931648731232,
    .53695642948151,
    .43875110149384,
    .43893754482269,
    .36897498369217,
    .39383068680763,
    .35268598794937,
    .30327013134956,
    .3206245303154,
    .29862868785858,
    .2883597612381,
    .30968201160431,
    .26627779006958,
    .29635512828827,
    .27021989226341,
    .25900682806969,
    .29855474829674,
    .26389503479004,
    .2940701842308,
    .24996083974838,
    .25805163383484,
    .26306444406509,
    .27853038907051,
    .26575443148613,
    .29165366292,
    .262396723032,
    .30186694860458,
    .35503962635994,
    .31088310480118,
    .37306407094002,
    .29435822367668,
    .32511350512505,
    .35352823138237,
    .36144828796387,
    .39527052640915,
    .38102087378502,
    .43927636742592,
    .42946752905846,
    .43780836462975,
    .49889534711838,
    .49156260490417,
    .50206583738327,
    .53814554214478,
    .55276554822922,
    .51174682378769,
    .53661912679672,
    .54144555330276,
    .4649658203125,
    .52955776453018,
    .48147654533386,
    .47327646613121,
    .46611019968987,
    .45984682440758,
    .48127228021622,
    .50998419523239,
    .65266174077988,
    .5583056807518,
    .80249065160751,
    .80990171432495,
    .87710189819336,
    .89202457666397,
    1.0564744472504,
    1.0080462694168,
    .87197554111481,
    .93521976470947,
    .9128600358963,
    .91022855043411,
    .7465353012085,
    .866335272789,
    .86956661939621,
    .84549117088318,
    .97585707902908,
    .87074065208435,
    .86343002319336,
    .93773847818375,
    .97884559631348,
    1.1054569482803,
    1.1484670639038,
    1.1322609186172,
    1.3402134180069,
    1.4842771291733,
    1.5056529045105,
    1.719556927681,
    1.8319338560104,
    1.5904501676559,
    1.6899375915527,
    1.8168371915817,
    1.6987046003342,
    1.8314251899719,
    1.7283667325974,
    1.4807628393173,
    1.308970451355,
    1.7375549077988,
    1.2797228097916,
    .98571860790253,
    1.1599444150925,
    1.1530816555023,
    1.13017141819,
    1.1908437013626,
    1.1662386655807,
    1.0540459156036,
    1.0774390697479,
    .95646268129349,
    1.1066728830338,
    .95817422866821,
    .95676136016846,
    1.1000070571899,
    .42435362935066,
    .81153392791748,
    .78478264808655,
    .91281259059906,
    .9670450091362,
    .94373846054077,
    .98408794403076,
    .9486455321312,
    1.0052901506424,
    1.0478744506836,
    1.095078587532,
    1.0925225019455,
    1.2685979604721,
    1.1865184307098,
    1.0648469924927,
    1.3658550977707,
    1.2376955747604,
    1.2978720664978,
    1.6663243770599,
    1.338112950325,
    1.0797605514526,
    1.1944576501846,
    1.1494234800339,
    1.153874874115,
    1.1408479213715,
    1.1294689178467,
    1.1464176177979,
    1.1174235343933,
    1.0820926427841,
    .98742854595184,
    1.0630278587341,
    .95385235548019,
    .97988063097,
    1.049503326416,
    1.0058189630508,
    1.0283635854721,
    1.0849515199661,
    .96609032154083,
    .97366327047348,
    1.0440692901611,
    1.1086683273315,
    .99680209159851,
    1.0642927885056,
    1.0725424289703,
    .8914600610733,
    .85157895088196,
    .97811859846115,
    .8258438706398,
    .71353197097778,
    .88130152225494,
    .81193578243256,
    .82894796133041,
    .9344978928566,
    .85150623321533,
    1.0080153942108,
    .9895287156105,
    1.1147927045822,
    1.2104271650314,
    1.0987895727158,
    1.23719227314,
    1.0314946174622,
    1.0577303171158,
    .8316445350647,
    .74243623018265,
    1.0848734378815,
    .88838368654251,
    1.0033586025238,
    1.0730868577957,
    .88500571250916,
    .82902699708939,
    .98530465364456,
    1.057307600975,
    1.0057097673416,
    1.1727533340454,
    1.2172684669495,
    1.0678850412369,
    1.3246995210648,
    1.0841422080994,
    2.0282983779907,
    1.0879902839661,
    1.289278626442,
    1.4674614667892,
    .75163400173187,
    1.2650294303894,
    1.4760826826096,
    1.2972749471664,
    1.3993507623672,
    1.8463147878647,
    1.4716247320175,
    2.2955448627472,
    .7857254743576,
    -.26799991726875,
    .71938097476959,
    1.0508332252502])

y = np.array([
    np.nan,
    29.841327667236,
    29.739316940308,
    29.886957168579,
    29.808752059937,
    29.978939056396,
    29.918973922729,
    30.143831253052,
    30.192686080933,
    30.113269805908,
    30.24062538147,
    30.27862739563,
    30.32836151123,
    30.519681930542,
    30.486276626587,
    30.67635345459,
    30.710220336914,
    30.73900604248,
    30.988555908203,
    31.01389503479,
    31.234069824219,
    31.199960708618,
    31.278051376343,
    31.383066177368,
    31.558530807495,
    31.645753860474,
    31.871654510498,
    31.912395477295,
    32.181865692139,
    32.635040283203,
    32.760883331299,
    33.223064422607,
    33.194358825684,
    33.425113677979,
    33.753528594971,
    34.061447143555,
    34.495269775391,
    34.781021118164,
    35.339279174805,
    35.729465484619,
    36.137809753418,
    36.798892974854,
    37.291561126709,
    37.802066802979,
    38.438148498535,
    39.052764892578,
    39.41174697876,
    39.936618804932,
    40.44144821167,
    40.564964294434,
    41.129554748535,
    41.381477355957,
    41.673278808594,
    41.966110229492,
    42.259845733643,
    42.681274414063,
    43.209983825684,
    44.352661132813,
    44.758304595947,
    46.402488708496,
    47.609901428223,
    48.977100372314,
    50.192024230957,
    52.05647277832,
    53.308044433594,
    53.871974945068,
    54.935218811035,
    55.81286239624,
    56.710227966309,
    56.846534729004,
    57.86633682251,
    58.769569396973,
    59.545490264893,
    60.975856781006,
    61.670738220215,
    62.463428497314,
    63.637741088867,
    64.878845214844,
    66.605453491211,
    68.248466491699,
    69.632263183594,
    71.940208435059,
    74.484275817871,
    76.70565032959,
    79.71955871582,
    82.73193359375,
    84.190444946289,
    86.389930725098,
    89.016830444336,
    90.798706054688,
    93.331428527832,
    95.128364562988,
    95.880767822266,
    96.308967590332,
    99.23755645752,
    99.379722595215,
    98.885719299316,
    99.959945678711,
    100.95308685303,
    101.93017578125,
    103.29084014893,
    104.4662399292,
    105.15404510498,
    106.17743682861,
    106.65645599365,
    108.10667419434,
    108.65817260742,
    109.45676422119,
    111.00000762939,
    109.12435150146,
    110.31153106689,
    110.98477935791,
    112.31281280518,
    113.66704559326,
    114.74374389648,
    115.98408508301,
    116.94864654541,
    118.20528411865,
    119.54787445068,
    120.99507904053,
    122.29251861572,
    124.36859893799,
    125.68651580811,
    126.46485137939,
    128.86585998535,
    130.1376953125,
    131.79786682129,
    135.06631469727,
    136.03811645508,
    136.17976379395,
    137.39445495605,
    138.34942626953,
    139.45387268066,
    140.54084777832,
    141.6294708252,
    142.84642028809,
    143.91741943359,
    144.88209533691,
    145.48742675781,
    146.66304016113,
    147.25386047363,
    148.17987060547,
    149.4494934082,
    150.40580749512,
    151.52836608887,
    152.88494873047,
    153.56610107422,
    154.47366333008,
    155.74406433105,
    157.20867919922,
    157.9967956543,
    159.26428222656,
    160.47253417969,
    160.79145812988,
    161.25157165527,
    162.47811889648,
    162.82585144043,
    162.91352844238,
    164.08129882813,
    164.71192932129,
    165.52894592285,
    166.83448791504,
    167.55149841309,
    169.10801696777,
    170.28953552246,
    172.0147857666,
    173.9104309082,
    174.99877929688,
    176.83720397949,
    177.43148803711,
    178.45771789551,
    178.43165588379,
    178.44242858887,
    180.38487243652,
    180.88838195801,
    182.20335388184,
    183.67309570313,
    184.08500671387,
    184.5290222168,
    185.88529968262,
    187.35731506348,
    188.40570068359,
    190.27276611328,
    192.01727294922,
    192.8678894043,
    195.12471008301,
    195.78413391113,
    201.22830200195,
    200.48799133301,
    201.98927307129,
    204.16746520996,
    202.65162658691,
    204.83903503418,
    207.39608764648,
    208.63526916504,
    210.53234863281,
    214.34130859375,
    215.4686126709,
    220.9055480957,
    217.67472839355,
    211.90599060059,
    213.39038085938,
    215.51982116699])

resid = np.array([
    np.nan,
    -.6913286447525,
    -.38931575417519,
    -.51695597171783,
    -.26875102519989,
    -.42893922328949,
    -.16897420585155,
    -.30383053421974,
    -.38268667459488,
    -.19326950609684,
    -.26062506437302,
    -.2386272996664,
    -.11836158484221,
    -.29968178272247,
    -.10627794265747,
    -.23635374009609,
    -.2302208840847,
    -.04900583252311,
    -.23855529725552,
    -.07389451563358,
    -.28406995534897,
    -.17996114492416,
    -.15805125236511,
    -.10306460410357,
    -.17853192985058,
    -.06575367599726,
    -.22165396809578,
    -.03239719197154,
    .09813265502453,
    -.18503764271736,
    .08911459892988,
    -.32306101918221,
    -.09436126798391,
    -.02511045895517,
    -.05352900549769,
    .03854943066835,
    -.09526748210192,
    .11897913366556,
    -.0392786487937,
    -.02946598827839,
    .16219010949135,
    .00110464950558,
    .00843739509583,
    .09793642908335,
    .06185294687748,
    -.15276403725147,
    -.01174682471901,
    -.03661911562085,
    -.34144860506058,
    .0350341796875,
    -.22955468297005,
    -.18147730827332,
    -.17327722907066,
    -.16611096262932,
    -.05984530970454,
    .01872771047056,
    .49001583456993,
    -.15266172587872,
    .84169203042984,
    .39751008152962,
    .4900975227356,
    .32289886474609,
    .80797618627548,
    .24352477490902,
    -.30804550647736,
    .12802444398403,
    -.03521826863289,
    -.01286232378334,
    -.61022931337357,
    .15346619486809,
    .03366623818874,
    -.06956738233566,
    .45450806617737,
    -.17585784196854,
    -.07074139267206,
    .236572265625,
    .26226228475571,
    .62115287780762,
    .49454152584076,
    .2515344619751,
    .96773761510849,
    1.059788107872,
    .71571981906891,
    1.2943501472473,
    1.1804445981979,
    -.13193695247173,
    .50954836606979,
    .81006240844727,
    .08316437155008,
    .70129698514938,
    .0685763657093,
    -.72836673259735,
    -.88076442480087,
    1.191029548645,
    -1.1375564336777,
    -1.4797197580338,
    -.08571709692478,
    -.15994438529015,
    -.15308164060116,
    .16982398927212,
    .00916088558733,
    -.36624330282211,
    -.0540459305048,
    -.47744059562683,
    .34354037046432,
    -.40667590498924,
    -.15817116200924,
    .44324016571045,
    -2.3000116348267,
    .37564942240715,
    -.11153698712587,
    .41522192955017,
    .38718286156654,
    .13296109437943,
    .25625845789909,
    .01591204665601,
    .25135138630867,
    .2947128713131,
    .35212710499763,
    .20491683483124,
    .80747896432877,
    .13140361011028,
    -.28651690483093,
    1.0351514816284,
    .03413881734014,
    .36231052875519,
    1.6021218299866,
    -.36632135510445,
    -.93810379505157,
    .02023027092218,
    -.19445763528347,
    -.04941740259528,
    -.05388405546546,
    -.04084182903171,
    .0705279931426,
    -.04641156643629,
    -.11742353439331,
    -.38209563493729,
    .11257757246494,
    -.36303088068962,
    -.05385848507285,
    .22011630237103,
    -.04950327426195,
    .09418711811304,
    .27163949608803,
    -.28494849801064,
    -.06609643250704,
    .22633366286755,
    .35593989491463,
    -.20867441594601,
    .20319482684135,
    .13570418953896,
    -.57254236936569,
    -.3914600610733,
    .24842712283134,
    -.47811862826347,
    -.62584692239761,
    .28646802902222,
    -.18130460381508,
    -.01193272508681,
    .37104898691177,
    -.13449484109879,
    .54850292205811,
    .19198158383369,
    .61046212911606,
    .6852103471756,
    -.01043024007231,
    .60122263431549,
    -.43720445036888,
    -.03149457275867,
    -.85771811008453,
    -.73165369033813,
    .85756987333298,
    -.38487648963928,
    .31161326169968,
    .39665061235428,
    -.47309604287148,
    -.38500571250916,
    .3709699511528,
    .4147045314312,
    .04268322139978,
    .69430238008499,
    .52724367380142,
    -.2172684520483,
    .93211495876312,
    -.42470565438271,
    3.4158577919006,
    -1.8283013105392,
    .21201276779175,
    .7107213139534,
    -2.2674646377563,
    .92237722873688,
    1.0809636116028,
    -.05808337032795,
    .49772322177887,
    1.9626487493515,
    -.34431591629982,
    3.1413819789886,
    -4.016538143158,
    -5.5007371902466,
    .76500922441483,
    1.078607916832,
    .86516714096069])

yr = np.array([
    np.nan,
    -.6913286447525,
    -.38931575417519,
    -.51695597171783,
    -.26875102519989,
    -.42893922328949,
    -.16897420585155,
    -.30383053421974,
    -.38268667459488,
    -.19326950609684,
    -.26062506437302,
    -.2386272996664,
    -.11836158484221,
    -.29968178272247,
    -.10627794265747,
    -.23635374009609,
    -.2302208840847,
    -.04900583252311,
    -.23855529725552,
    -.07389451563358,
    -.28406995534897,
    -.17996114492416,
    -.15805125236511,
    -.10306460410357,
    -.17853192985058,
    -.06575367599726,
    -.22165396809578,
    -.03239719197154,
    .09813265502453,
    -.18503764271736,
    .08911459892988,
    -.32306101918221,
    -.09436126798391,
    -.02511045895517,
    -.05352900549769,
    .03854943066835,
    -.09526748210192,
    .11897913366556,
    -.0392786487937,
    -.02946598827839,
    .16219010949135,
    .00110464950558,
    .00843739509583,
    .09793642908335,
    .06185294687748,
    -.15276403725147,
    -.01174682471901,
    -.03661911562085,
    -.34144860506058,
    .0350341796875,
    -.22955468297005,
    -.18147730827332,
    -.17327722907066,
    -.16611096262932,
    -.05984530970454,
    .01872771047056,
    .49001583456993,
    -.15266172587872,
    .84169203042984,
    .39751008152962,
    .4900975227356,
    .32289886474609,
    .80797618627548,
    .24352477490902,
    -.30804550647736,
    .12802444398403,
    -.03521826863289,
    -.01286232378334,
    -.61022931337357,
    .15346619486809,
    .03366623818874,
    -.06956738233566,
    .45450806617737,
    -.17585784196854,
    -.07074139267206,
    .236572265625,
    .26226228475571,
    .62115287780762,
    .49454152584076,
    .2515344619751,
    .96773761510849,
    1.059788107872,
    .71571981906891,
    1.2943501472473,
    1.1804445981979,
    -.13193695247173,
    .50954836606979,
    .81006240844727,
    .08316437155008,
    .70129698514938,
    .0685763657093,
    -.72836673259735,
    -.88076442480087,
    1.191029548645,
    -1.1375564336777,
    -1.4797197580338,
    -.08571709692478,
    -.15994438529015,
    -.15308164060116,
    .16982398927212,
    .00916088558733,
    -.36624330282211,
    -.0540459305048,
    -.47744059562683,
    .34354037046432,
    -.40667590498924,
    -.15817116200924,
    .44324016571045,
    -2.3000116348267,
    .37564942240715,
    -.11153698712587,
    .41522192955017,
    .38718286156654,
    .13296109437943,
    .25625845789909,
    .01591204665601,
    .25135138630867,
    .2947128713131,
    .35212710499763,
    .20491683483124,
    .80747896432877,
    .13140361011028,
    -.28651690483093,
    1.0351514816284,
    .03413881734014,
    .36231052875519,
    1.6021218299866,
    -.36632135510445,
    -.93810379505157,
    .02023027092218,
    -.19445763528347,
    -.04941740259528,
    -.05388405546546,
    -.04084182903171,
    .0705279931426,
    -.04641156643629,
    -.11742353439331,
    -.38209563493729,
    .11257757246494,
    -.36303088068962,
    -.05385848507285,
    .22011630237103,
    -.04950327426195,
    .09418711811304,
    .27163949608803,
    -.28494849801064,
    -.06609643250704,
    .22633366286755,
    .35593989491463,
    -.20867441594601,
    .20319482684135,
    .13570418953896,
    -.57254236936569,
    -.3914600610733,
    .24842712283134,
    -.47811862826347,
    -.62584692239761,
    .28646802902222,
    -.18130460381508,
    -.01193272508681,
    .37104898691177,
    -.13449484109879,
    .54850292205811,
    .19198158383369,
    .61046212911606,
    .6852103471756,
    -.01043024007231,
    .60122263431549,
    -.43720445036888,
    -.03149457275867,
    -.85771811008453,
    -.73165369033813,
    .85756987333298,
    -.38487648963928,
    .31161326169968,
    .39665061235428,
    -.47309604287148,
    -.38500571250916,
    .3709699511528,
    .4147045314312,
    .04268322139978,
    .69430238008499,
    .52724367380142,
    -.2172684520483,
    .93211495876312,
    -.42470565438271,
    3.4158577919006,
    -1.8283013105392,
    .21201276779175,
    .7107213139534,
    -2.2674646377563,
    .92237722873688,
    1.0809636116028,
    -.05808337032795,
    .49772322177887,
    1.9626487493515,
    -.34431591629982,
    3.1413819789886,
    -4.016538143158,
    -5.5007371902466,
    .76500922441483,
    1.078607916832,
    .86516714096069])

mse = np.array([
    .80370712280273,
    .80370712280273,
    .67928272485733,
    .66423606872559,
    .65418779850006,
    .64722007513046,
    .64226144552231,
    .63866710662842,
    .63602674007416,
    .63406819105148,
    .6326048374176,
    .63150554895401,
    .63067644834518,
    .63004916906357,
    .62957346439362,
    .62921214103699,
    .62893730401993,
    .62872803211212,
    .6285685300827,
    .62844693660736,
    .62835419178009,
    .62828344106674,
    .62822943925858,
    .62818819284439,
    .62815672159195,
    .6281327009201,
    .62811434268951,
    .62810027599335,
    .62808960676193,
    .6280814409256,
    .6280751824379,
    .62807041406631,
    .62806677818298,
    .62806397676468,
    .62806183099747,
    .62806022167206,
    .62805896997452,
    .6280580163002,
    .62805730104446,
    .62805676460266,
    .62805634737015,
    .62805598974228,
    .6280557513237,
    .62805557250977,
    .62805545330048,
    .62805533409119,
    .6280552148819,
    .62805515527725,
    .62805509567261,
    .62805509567261,
    .62805503606796,
    .62805503606796,
    .62805503606796,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332,
    .62805497646332])

stdp = np.array([
    .86132872104645,
    .86132872104645,
    .58931648731232,
    .53695642948151,
    .43875110149384,
    .43893754482269,
    .36897498369217,
    .39383068680763,
    .35268598794937,
    .30327013134956,
    .3206245303154,
    .29862868785858,
    .2883597612381,
    .30968201160431,
    .26627779006958,
    .29635512828827,
    .27021989226341,
    .25900682806969,
    .29855474829674,
    .26389503479004,
    .2940701842308,
    .24996083974838,
    .25805163383484,
    .26306444406509,
    .27853038907051,
    .26575443148613,
    .29165366292,
    .262396723032,
    .30186694860458,
    .35503962635994,
    .31088310480118,
    .37306407094002,
    .29435822367668,
    .32511350512505,
    .35352823138237,
    .36144828796387,
    .39527052640915,
    .38102087378502,
    .43927636742592,
    .42946752905846,
    .43780836462975,
    .49889534711838,
    .49156260490417,
    .50206583738327,
    .53814554214478,
    .55276554822922,
    .51174682378769,
    .53661912679672,
    .54144555330276,
    .4649658203125,
    .52955776453018,
    .48147654533386,
    .47327646613121,
    .46611019968987,
    .45984682440758,
    .48127228021622,
    .50998419523239,
    .65266174077988,
    .5583056807518,
    .80249065160751,
    .80990171432495,
    .87710189819336,
    .89202457666397,
    1.0564744472504,
    1.0080462694168,
    .87197554111481,
    .93521976470947,
    .9128600358963,
    .91022855043411,
    .7465353012085,
    .866335272789,
    .86956661939621,
    .84549117088318,
    .97585707902908,
    .87074065208435,
    .86343002319336,
    .93773847818375,
    .97884559631348,
    1.1054569482803,
    1.1484670639038,
    1.1322609186172,
    1.3402134180069,
    1.4842771291733,
    1.5056529045105,
    1.719556927681,
    1.8319338560104,
    1.5904501676559,
    1.6899375915527,
    1.8168371915817,
    1.6987046003342,
    1.8314251899719,
    1.7283667325974,
    1.4807628393173,
    1.308970451355,
    1.7375549077988,
    1.2797228097916,
    .98571860790253,
    1.1599444150925,
    1.1530816555023,
    1.13017141819,
    1.1908437013626,
    1.1662386655807,
    1.0540459156036,
    1.0774390697479,
    .95646268129349,
    1.1066728830338,
    .95817422866821,
    .95676136016846,
    1.1000070571899,
    .42435362935066,
    .81153392791748,
    .78478264808655,
    .91281259059906,
    .9670450091362,
    .94373846054077,
    .98408794403076,
    .9486455321312,
    1.0052901506424,
    1.0478744506836,
    1.095078587532,
    1.0925225019455,
    1.2685979604721,
    1.1865184307098,
    1.0648469924927,
    1.3658550977707,
    1.2376955747604,
    1.2978720664978,
    1.6663243770599,
    1.338112950325,
    1.0797605514526,
    1.1944576501846,
    1.1494234800339,
    1.153874874115,
    1.1408479213715,
    1.1294689178467,
    1.1464176177979,
    1.1174235343933,
    1.0820926427841,
    .98742854595184,
    1.0630278587341,
    .95385235548019,
    .97988063097,
    1.049503326416,
    1.0058189630508,
    1.0283635854721,
    1.0849515199661,
    .96609032154083,
    .97366327047348,
    1.0440692901611,
    1.1086683273315,
    .99680209159851,
    1.0642927885056,
    1.0725424289703,
    .8914600610733,
    .85157895088196,
    .97811859846115,
    .8258438706398,
    .71353197097778,
    .88130152225494,
    .81193578243256,
    .82894796133041,
    .9344978928566,
    .85150623321533,
    1.0080153942108,
    .9895287156105,
    1.1147927045822,
    1.2104271650314,
    1.0987895727158,
    1.23719227314,
    1.0314946174622,
    1.0577303171158,
    .8316445350647,
    .74243623018265,
    1.0848734378815,
    .88838368654251,
    1.0033586025238,
    1.0730868577957,
    .88500571250916,
    .82902699708939,
    .98530465364456,
    1.057307600975,
    1.0057097673416,
    1.1727533340454,
    1.2172684669495,
    1.0678850412369,
    1.3246995210648,
    1.0841422080994,
    2.0282983779907,
    1.0879902839661,
    1.289278626442,
    1.4674614667892,
    .75163400173187,
    1.2650294303894,
    1.4760826826096,
    1.2972749471664,
    1.3993507623672,
    1.8463147878647,
    1.4716247320175,
    2.2955448627472,
    .7857254743576,
    -.26799991726875,
    .71938097476959,
    1.0508332252502])

icstats = np.array([
    202,
    np.nan,
    -239.91961140777,
    5,
    489.83922281554,
    506.38056130255])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    stdp=stdp,
    icstats=icstats
)
