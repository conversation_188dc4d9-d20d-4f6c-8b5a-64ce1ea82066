'''Generated Random Processes for tests

autogenerated by savervs.py

'''

from numpy import array

from statsmodels.tools.testing import Holder

rvsdata = Holder()
rvsdata.comment = 'generated data, divide by 1000, see savervs'
rvsdata.xarma32 = array([
    -1271, -1222, -840, -169, -1016, -980, -1272, -926, 445, 833,
    -91, -1974, -2231, -549, 424, 238, -1665, -1815, 685, 3361,
    1912, -1931, -3555, -1817, 387, 730, -1154, -702, 973, 1340,
    -161, 276, 200, 1785, 834, -1469, -1593, -134, 555, -422,
    -2314, -1326, -2268, -3579, -3049, -930, 1155, 962, -644, -217,
    -561, 224, 810, 2445, 2710, 2152, 502, 21, 164, -499,
    -1093, -492, 531, -605, -1535, -2081, -3816, -2257, 487, 2134,
    1785, 1495, 1259, 1895, 1339, 617, 1143, 385, -1220, -738,
    1171, 1047, -234, -107, -1458, -1244, -2737, 33, 2373, 2749,
    2725, 3331, 1054, 418, 1231, -1171, -1446, -1187, 863, 1386,
    757, 734, 283, -735, 550, 417, -236, 324, 318, -102,
    2126, 3246, 2358, 2156, 726, -983, -803, -242, -500, -13,
    49, 308, -227, 243, -612, -2329, -2476, -3441, -5435, -4693,
    -2538, -2159, -2656, -906, -211, -288, 1777, 1363, 564, -2035,
    -1134, -609, -1112, 560, 658, 1533, 796, 523, 456, 76,
    -1164, -749, -1084, -3218, -2107, -310, -686, -1625, 2008, 4155,
    1650, -1086, -673, 1634, 1999, 449, -1077, -648, -155, -327,
    228, 1295, 2036, 542, -197, -451, -1554, -2416, -2066, -2146,
    -1524, -1976, -2962, -2621, -2313, -2052, -3314, -2363, -1522, -3305,
    -3445, -3206, -1501, 2029, 1963, 1168, 2050, 2927, 2019, 84,
    213, 1783, 617, -767, -425, 739, 281, 506, -749, -938,
    -284, -147, 51, 1296, 3033, 2263, 1409, -1702, -819, -1295,
    -1831, -539, 1327, 1954, 1473, -1535, -1187, -1310, 380, 1621,
    2035, 2234, 559, 51, -1071, 590, 2128, 1483, 848, 1198,
    2707, 1447, -629, 237, 909, 453, -734, -802, 1026, 521,
    -9, 919, 441, -118, -1073, -2428, 98, 823, 102, -438,
    -233, -613, 440, 1143, -743, -1345, 186, -1999, -2351, -887,
    -584, -883, -623, -1522, 974, 2318, 1329, -523, -2599, -1555,
    826, -859, -2790, -2753, 807, 1889, -95, -1454, 443, 845,
    -291, 1516, 2804, 1018, 402, -446, -1721, -1824, 1678, 2889,
    -663, -560, 628, 1213, 520, -1344, -3029, -3100, -1603, -1480,
    -1667, -3356, -4405, -2556, 532, 1602, -15, 646, 2279, 1893,
    -945, -258, 344, -316, 1130, 1119, 695, 276, 56, -682,
    -610, 412, 1058, 259, 746, 1197, 1959, 1896, 127, -1301,
    1036, 3094, 5213, 3846, 1728, 40, -520, -173, 330, -480,
    649, 1621, 1622, -1011, -1851, -2687, -756, 401, 1888, 2372,
    4153, 2531, -150, 485, 2600, 2193, -1238, -2702, -184, 1336,
    370, -1196, -1737, 637, 634, 77, -1314, -688, -1375, -1973,
    -1229, -1414, -2230, -1922, -584, 93, 180, 2158, 2976, 1433,
    -173, -1073, -1362, -446, 242, 7, 354, 332, 2003, 1866,
    -729, -1446, -294, 2438, 3955, 1829, 485, 1028, 981, 1335,
    513, -1386, -2583, -1063, 465, 1104, 85, -892, -78, 766,
    1995, 891, -170, 2, -428, -562, -1078, -2591, -2077, -135,
    -238, -1150, -1207, -185, -46, -1319, -1829, -1409, -926, 576,
    1119, 454, -747, -538, -739, -2994, -3052, -1626, -2472, -1340,
    -254, -972, -1182, -258, 831, 876, -244, -724, -208, -428,
    -110, 188, -2187, -2695, -1161, 597, 1492, 1594, -403, 695,
    1834, 1737, 586, -740, 259, -714, -1607, -1082, -365, 2040,
    604, -1253, -1269, -419, -713, -482, 1379, 2335, 1730, 325,
    -1377, -1721, -1762, -602, -1224, -839, 70, -1058, -118, -691,
    -1397, -245, -291, -648, -1489, -1088, -1083, -160, 1310, 169,
    -1539, -1558, -2095, -3421, -1609, -465, -867, 311, 272, -157,
    -936, -1003, -492, -1526, -2179, -1237, -662, -144, 638, 596,
    -629, -1893, -671, 324, 408, 367, 1438, 4568, 2576, 677,
    701, 2667, 1288, 449, -357, 776, 2250, 2324, 968, 245,
    1432, 1597, 843, 88, -274, -256, 830, 348, 534, 140,
    -560, -1582, -2012, -287, 1470, -729, -2398, -1433, -1409, -1547,
    70, 1438, 2246, 408, -293, -566, 374, 1793, 2355, 1104,
    358, 2301, 2994, 572, 278, 508, -2406, -2767, -1216, -231,
    -1717, -1038, 2015, 1469, 1471, 1395, 860, 1148, 1211, 1189,
    494, -536, 383, -136, -2171, -2334, -1181, -294, -841, -2051,
    -3304, -2254, -926, -811, 160, 1960, 2945, 2466, 1922, 2833,
    2421, 1197, 3025, 4033, 3210, 1497, 1912, 1138, 174, -630,
    -2423, -999, 296, 1519, 2061, 1400, -424, -609, -978, -1747,
    -1637, -2454, -1547, 885, 2065, 1530, -1956, 846, 2811, 3105,
    2220, 2732, 4631, 3504, 1996, 246, -419, -1541, -1955, -3171,
    -2742, -811, -318, -1303, -2002, -997, -487, -2089, -3453, -3373,
    -1940, -620, 384, 365, -133, -1300, -833, -1544, -1711, -1981,
    -315, -155, -1995, -2384, -4010, -5394, -6186, -3794, -1829, -2637,
    -4255, -2014, 282, -174, -2623, -2023, -749, -168, -2387, -3959,
    -4101, -2004, -2070, -2468, -1831, -1518, 606, 305, 684, 2183,
    1218, -1008, -2261, -1276, -99, 889, 740, -525, -1786, -1716,
    -452, -872, -1384, -1867, -547, -900, -1464, -1898, -1493, -990,
    965, 810, 636, -335, -57, 1761, 2837, 773, 215, 920,
    483, -234, 1301, 2610, 3083, 2329, 920, -827, 22, 4317,
    5366, 3711, 2220, 1356, 198, -1385, 656, 1163, -370, -1721,
    -1005, -832, -1455, -1485, 221, -1445, -1502, -79, -4, -599,
    -850, -507, 902, 1909, 1642, -326, -3379, -5642, -7068, -4275,
    -1044, 528, 548, 249, -1384, -2485, -1533, -1776, -2930, -2058,
    -1721, -475, -166, -1761, -2550, -1586, -240, -1584, -1954, 623,
    3826, 2094, -1004, -1782, -267, 2490, 3336, 2293, 189, -108,
    -315, -965, -125, 1201, 360, -544, -1602, -2150, -901, 1430,
    968, -1100, 505, 2880, 2554, 928, 918, 689, -2829, -2478,
    -2904, -1615, -242, 243, -1668, -877, 2385, 543, -2462, -1762,
    470, 1344, 1493, 1624, 257, -1833, -1947, -805, -413, 905,
    2909, 3272, 1148, -1473, -2368, -1054, -2143, -4330, -3257, -1939,
    -1831, -414, -1157, -1212, -1644, -1360, -2409, -4136, -5747, -3415,
    -1752, 373, -1680, -1267, 2267, 2701, 1101, -1714, -3138, -3153,
    -3256, -3328, -2661, -879, 2115, 1795, -324, -1930, -1432, -1613,
    -2301, -1401, 88, 1369, 1063, -854, -2125, 243, 1683, 2011,
    2646, 1289, -938, -1205, -1214, 562, 2641, 3335, 2858, 2650,
    1965, 478, 1391, 486, 255, -1764, -813, 84, -453, -809,
    -1203, -1590, 730, 2059, 234, -319, 0, 624, 1273, 1470,
    1882, 2215, 1611, 485, -16, 397, 593, -95, 125, 1435,
    2673, 3073, 2262, 1803, 983, 666, 1516, 2821, 2395, 299,
    86, 1150, 1214, 751, -1096, -962, -39, -366, -2125, -2086,
    -1032, -966, -863, -1522, -1793, 1228, 207, -2243, -1916, -1320,
    -1530, -2318, -1050, -663, -1137, -2035, -1198, -246, 753, -185,
    -709, -231, -1111, -1121, -11, 976, 555, -1947, -1304, 807,
    529, 231, -285, -553, -695, -2006, -1090, -424, 318, -1113])
rvsdata.name = 'rvsdata'
rvsdata.xnormal = array([
    -1271, 176, -296, 327, -973, 228, -819, 107, 975, 82,
    -477, -1492, -403, 695, 212, 91, -1549, -45, 1557, 1947,
    -785, -2139, -1264, 295, 806, 278, -1244, 787, 752, 173,
    -738, 969, -646, 1811, -990, -1369, 72, 408, 169, -587,
    -1517, 720, -2150, -1233, -121, 682, 1268, -29, -802, 679,
    -1041, 934, 344, 1788, 486, 460, -834, 40, -93, -751,
    -374, 345, 500, -1167, -387, -966, -2369, 1119, 1148, 1193,
    411, 626, 45, 960, -293, 11, 806, -866, -1043, 574,
    1072, -328, -381, 433, -1857, 409, -2190, 2614, 1005, 864,
    1243, 1268, -1701, 680, 560, -2567, 639, -663, 1513, 215,
    69, 498, -504, -771, 1392, -739, -131, 744, -382, -158,
    2394, 712, 162, 1064, -1146, -1062, 248, -171, -411, 665,
    -236, 350, -503, 645, -1105, -1447, -337, -2050, -2539, -151,
    85, -840, -555, 1235, -427, 106, 2227, -828, 252, -2248,
    992, -737, -559, 1801, -593, 1438, -583, 342, -10, -331,
    -1053, 466, -1020, -2163, 1003, 224, -794, -406, 3338, 1021,
    -1157, -788, 242, 1219, 267, -455, -843, 183, -196, -181,
    699, 822, 825, -920, 110, -482, -1332, -843, -256, -989,
    232, -1082, -1221, -200, -800, -344, -1779, 559, -485, -2241,
    -84, -1173, 701, 2685, -395, 644, 1374, 741, -144, -788,
    542, 1044, -1150, -296, 281, 485, -495, 829, -1385, 80,
    192, -237, 340, 1213, 1634, -264, 479, -2698, 1282, -1759,
    -422, 1003, 1015, 668, 332, -2367, 835, -1347, 1532, 828,
    766, 907, -1122, 265, -1357, 1658, 879, -199, 433, 532,
    1482, -925, -812, 1081, -191, -126, -637, -45, 1306, -863,
    326, 954, -806, 42, -885, -1504, 2167, -496, -63, -19,
    -61, -654, 1153, 340, -1586, 97, 836, -2868, 439, 380,
    -652, -34, 197, -1342, 2507, 481, -228, -748, -1941, 596,
    1137, -1978, -857, -546, 2208, 151, -864, -446, 1297, -507,
    -417, 2265, 596, -1011, 719, -1112, -1279, -184, 2721, 371,
    -2244, 1511, -127, 365, -53, -1399, -1628, -736, 312, -785,
    -182, -2070, -1452, 640, 1479, 648, -754, 1396, 1005, -183,
    -1661, 1296, -547, -532, 1901, -511, 232, -27, -191, -734,
    140, 647, 406, -449, 997, 204, 1035, 352, -1083, -788,
    2025, 1127, 2903, -184, -197, -888, -536, 82, 279, -775,
    1426, 490, 362, -1900, -219, -1753, 1342, 166, 1677, 753,
    2518, -1078, -990, 1138, 1235, -197, -2026, -747, 1329, 255,
    -323, -722, -716, 1677, -746, 298, -1190, 509, -1420, -498,
    302, -996, -883, -12, 443, 139, 260, 2131, 620, -535,
    -443, -870, -671, 535, 213, -172, 612, -169, 1841, -195,
    -1629, -3, 265, 2091, 1611, -929, 225, 486, -338, 922,
    -582, -1433, -1072, 765, 424, 696, -541, -524, 612, 278,
    1405, -777, -163, 188, -805, 36, -692, -1680, 268, 810,
    -688, -359, -120, 386, -248, -1015, -387, -273, -158, 1263,
    271, -209, -716, 208, -738, -2268, -37, 5, -1793, 1277,
    46, -967, 151, 427, 579, 178, -621, -189, 167, -563,
    481, 93, -2388, -120, 359, 751, 946, 613, -1484, 1690,
    387, 285, -258, -870, 936, -1574, -400, 204, -70, 2254,
    -1548, -593, -89, -66, -599, 452, 1518, 658, 195, -496,
    -1363, -468, -759, 681, -1159, 579, 368, -1393, 1360, -1277,
    -474, 959, -779, -77, -864, 141, -632, 770, 1119, -1125,
    -857, -153, -1451, -1597, 1318, -337, -436, 1453, -619, -64,
    -625, -214, 78, -1334, -519, 313, -293, 446, 719, -93,
    -860, -1006, 823, 57, 199, 332, 1112, 3079, -1616, 338,
    298, 1515, -1213, 603, -811, 1023, 1171, 464, -372, -24,
    1105, -43, -1, -208, -340, -102, 970, -632, 743, -459,
    -520, -977, -671, 1096, 974, -1956, -601, 251, -1197, -108,
    1305, 661, 1135, -1164, 195, -628, 708, 1211, 773, -470,
    102, 1923, 405, -1286, 932, -349, -2927, 277, 144, -19,
    -1333, 988, 2027, -952, 1495, 91, -288, 784, 127, 341,
    -324, -670, 967, -1015, -1599, -98, -9, 157, -541, -1040,
    -1576, 297, 67, -285, 1094, 1433, 1051, 440, 491, 1410,
    -145, -138, 2498, 764, 408, -237, 1099, -888, -184, -541,
    -1975, 1272, 87, 1229, 908, -97, -1121, 168, -949, -891,
    -88, -1521, 596, 1512, 747, 259, -2640, 3297, 236, 1135,
    500, 1288, 2137, -399, 380, -1022, -439, -1345, -514, -1828,
    -69, 770, -307, -693, -606, 370, -290, -1584, -1193, -834,
    148, 404, 771, 18, -207, -1068, 393, -1392, -163, -807,
    1152, -564, -1495, -210, -2692, -1930, -2043, 660, -58, -1329,
    -1511, 1339, 458, -536, -1669, 511, -210, 167, -2028, -1333,
    -1271, 661, -1274, -334, 64, -619, 1818, -811, 1078, 1446,
    -927, -1106, -984, 181, 235, 902, 44, -836, -1021, -298,
    479, -916, -219, -811, 804, -1060, -300, -710, -163, -115,
    1647, -480, 582, -807, 412, 1486, 972, -1188, 649, 326,
    -605, -109, 1607, 847, 1140, 266, -492, -1229, 912, 3582,
    914, 580, 283, -375, -834, -1206, 1985, -468, -814, -702,
    211, -700, -608, -113, 1086, -2231, 662, 581, -565, -131,
    -197, -39, 1113, 863, 236, -1199, -2557, -2587, -2859, 948,
    822, 911, 448, 71, -1579, -959, 292, -1273, -1206, 474,
    -908, 929, -126, -1497, -608, 106, 371, -1552, 61, 1758,
    2286, -1107, -1079, -534, 404, 2099, 1035, 219, -997, 143,
    -666, -652, 850, 788, -780, -118, -1156, -878, 675, 1530,
    -576, -1022, 1862, 1282, 75, -105, 604, -507, -3169, 907,
    -2181, 792, 540, 180, -1660, 1113, 2179, -2179, -1223, 567,
    560, 544, 906, 580, -1054, -1474, -186, 110, -189, 1452,
    1823, 684, -856, -1508, -974, 353, -1953, -1918, 418, -598,
    -513, 1332, -1457, 226, -905, -78, -1575, -1908, -2347, 923,
    -460, 1745, -2262, 1119, 2445, -200, 25, -1892, -1465, -1012,
    -1193, -797, -204, 784, 2291, -382, -782, -1097, -103, -1085,
    -802, 521, 547, 1020, 17, -1255, -948, 1733, 352, 971,
    1444, -955, -1251, -38, -844, 1473, 1673, 1071, 687, 818,
    -109, -758, 1446, -1260, 507, -2043, 1091, -143, -573, 40,
    -705, -693, 1992, 582, -1145, 532, -176, 341, 802, 455,
    858, 713, -47, -389, -137, 288, 63, -443, 499, 1048,
    1202, 1044, 157, 472, -423, 135, 978, 1325, 104, -958,
    465, 564, -14, 220, -1498, 330, 158, -601, -1414, -14,
    -29, -535, 186, -954, -468, 2492, -1869, -1194, 345, -835,
    -578, -838, 802, -596, -418, -898, 414, 91, 851, -824,
    -34, 193, -1276, 208, 593, 538, -175, -1954, 860, 843,
    -610, 617, -511, -339, -261, -1495, 833, -298, 637, -1384])
rvsdata.xar2 = array([
    -1271, -841, -333, 481, -422, -350, -889, -428, 1077, 1158,
    -89, -2142, -2073, 108, 1335, 1105, -1332, -1663, 892, 3493,
    1563, -2635, -4154, -1710, 1515, 2345, -125, -486, 426, 757,
    -346, 314, -222, 1476, 302, -1866, -1571, 84, 1022, 189,
    -1877, -876, -1912, -2325, -1025, 1024, 2600, 1539, -871, -787,
    -1235, 339, 1233, 2604, 1953, 721, -1234, -1307, -522, -514,
    -525, 183, 908, -532, -1267, -1713, -3107, -509, 2294, 3282,
    1890, 497, -502, 310, 206, 21, 720, -301, -1644, -591,
    1421, 1104, -209, -286, -1981, -1033, -2026, 1509, 3226, 2689,
    1781, 1348, -1514, -1205, 353, -1682, -883, -529, 1531, 1704,
    667, 180, -694, -1416, 607, 454, -71, 460, 22, -371,
    2086, 2567, 1172, 719, -1157, -2347, -1052, 161, 243, 779,
    266, 174, -497, 160, -729, -2110, -1661, -2324, -3568, -1843,
    394, 397, -434, 689, 342, 35, 2084, 822, -133, -2765,
    -1154, -277, -204, 1777, 931, 1294, -13, -315, -256, -379,
    -1228, -327, -667, -2533, -690, 938, 302, -633, 2681, 3483,
    289, -2298, -1741, 975, 1918, 592, -1329, -1176, -472, 29,
    959, 1575, 1606, -423, -1031, -1095, -1693, -1649, -729, -748,
    -1, -709, -1788, -1275, -926, -447, -1674, -557, -93, -2038,
    -1667, -1488, 345, 3705, 2396, 709, 743, 981, 269, -1063,
    -443, 1222, 48, -868, -438, 569, 180, 689, -924, -1003,
    -148, 146, 531, 1564, 2620, 1050, 9, -3216, -1295, -1187,
    -725, 1016, 2190, 1911, 766, -2710, -1716, -1364, 1298, 2549,
    2156, 1357, -1114, -1305, -1844, 835, 2470, 1359, 286, 81,
    1404, 157, -1388, -108, 416, 261, -636, -685, 1076, 340,
    60, 831, -170, -510, -1208, -2215, 999, 1410, 565, -272,
    -561, -967, 660, 1351, -835, -1247, 256, -2040, -1321, 344,
    283, 21, 72, -1294, 1436, 2277, 876, -1185, -3327, -1474,
    1622, 57, -1623, -1873, 1521, 2304, 219, -1423, 50, 244,
    -246, 1946, 2276, -164, -550, -1471, -2180, -1193, 2857, 3253,
    -1070, -971, -369, 556, 576, -1216, -2889, -2439, -195, 279,
    139, -2099, -3201, -871, 2383, 2990, 447, 258, 989, 479,
    -1772, -361, 50, -312, 1627, 947, 176, -360, -567, -1008,
    -383, 845, 1273, 147, 478, 513, 1206, 1061, -837, -1988,
    853, 2803, 4719, 2190, -804, -2626, -2234, -392, 1083, 288,
    1115, 1238, 795, -1883, -2123, -2510, 395, 1737, 2869, 2179,
    2827, 94, -2329, -772, 1781, 1614, -1626, -2855, -142, 1569,
    1003, -705, -1781, 605, 628, 498, -1106, -624, -1367, -1280,
    -38, -387, -1173, -757, 423, 856, 732, 2289, 2085, -11,
    -1494, -2060, -1572, 308, 1245, 670, 525, -84, 1511, 1056,
    -1540, -1763, -375, 2672, 3936, 884, -1037, -785, -447, 957,
    407, -1586, -2544, -477, 1314, 1986, 391, -1204, -547, 443,
    2033, 628, -676, -667, -1001, -431, -537, -1894, -979, 974,
    581, -382, -716, 4, 113, -926, -1185, -757, -172, 1504,
    1560, 287, -1266, -948, -864, -2485, -1593, -27, -1018, 476,
    936, -456, -682, 109, 1007, 929, -382, -959, -409, -410,
    357, 584, -2100, -2092, -265, 1585, 2346, 1697, -1299, -198,
    879, 1087, 172, -1276, -171, -1072, -1172, -197, 358, 2639,
    384, -1606, -1566, -516, -229, 528, 2055, 2038, 798, -876,
    -2463, -2000, -1127, 780, 28, 212, 523, -1080, 234, -549,
    -1030, 410, 64, -231, -1080, -608, -578, 611, 1897, 87,
    -1736, -1585, -1852, -2285, 415, 1138, 267, 1097, 125, -512,
    -1097, -836, -42, -949, -1257, -219, 161, 683, 1185, 513,
    -1042, -2096, -333, 839, 1037, 742, 1187, 3658, 717, -917,
    -794, 1338, 255, 138, -828, 291, 1818, 1772, 137, -801,
    396, 674, 341, -273, -729, -548, 895, 358, 582, -172,
    -949, -1650, -1517, 708, 2298, -472, -2127, -1215, -1106, -385,
    1550, 2094, 2035, -583, -1289, -1367, 258, 2102, 2325, 339,
    -790, 1122, 1697, -490, -308, -350, -3053, -1991, 78, 1039,
    -541, 36, 2326, 890, 1044, 481, -425, 204, 502, 641,
    -62, -1041, 166, -362, -1971, -1494, -219, 729, 151, -1283,
    -2678, -1204, 443, 671, 1409, 2225, 2127, 1029, 251, 1096,
    606, -201, 2034, 2492, 1384, -376, 106, -615, -729, -816,
    -2263, -130, 1114, 2185, 2099, 490, -1779, -1501, -1260, -1148,
    -377, -1248, -214, 1965, 2425, 1216, -2880, 385, 1984, 2530,
    1532, 1249, 2370, 873, -107, -1544, -1620, -1870, -1200, -1853,
    -952, 936, 917, -427, -1407, -542, -20, -1329, -2246, -1967,
    -303, 1145, 1838, 916, -393, -1841, -883, -1178, -664, -749,
    885, 518, -1523, -1687, -3280, -3711, -3372, -182, 1482, -52,
    -2294, -470, 1229, 683, -1737, -1220, -317, 524, -1451, -2756,
    -2751, -161, -27, -275, -142, -595, 1413, 617, 865, 1829,
    103, -1938, -2586, -919, 793, 1996, 1244, -839, -2314, -1730,
    252, 150, -225, -1066, 63, -476, -713, -1042, -641, -107,
    1883, 1079, 504, -944, -595, 1482, 2455, 36, -550, -132,
    -435, -391, 1512, 2252, 2185, 888, -874, -2372, -549, 4330,
    4652, 2136, -334, -1711, -2035, -1979, 1419, 1657, -198, -1689,
    -1041, -688, -639, -279, 1182, -1146, -845, 477, 240, -178,
    -459, -318, 1088, 1893, 1206, -1180, -4105, -5281, -5031, -437,
    2988, 3519, 1770, -273, -2682, -2968, -741, -383, -1141, -248,
    -535, 625, 641, -1297, -1966, -818, 700, -584, -756, 1445,
    3821, 1227, -2007, -2753, -795, 2840, 3704, 1762, -1440, -1890,
    -1458, -873, 881, 1929, 323, -824, -1976, -2047, 26, 2574,
    1471, -1132, 221, 2024, 1584, 150, -68, -636, -3644, -1691,
    -1712, 268, 1610, 1334, -1398, -672, 2340, 29, -2370, -1343,
    670, 1752, 1973, 1282, -1015, -2927, -2020, -43, 787, 2103,
    3111, 2122, -715, -3141, -3129, -580, -852, -2310, -1003, -246,
    -208, 1288, -322, -675, -1284, -768, -1547, -2762, -3783, -722,
    853, 2789, -458, -641, 2161, 1850, 424, -2477, -3659, -2700,
    -1523, -666, 25, 1137, 3188, 1600, -1096, -2774, -1774, -1117,
    -809, 432, 1297, 1841, 842, -1502, -2570, 428, 1979, 2341,
    2327, -264, -2626, -2007, -1137, 1567, 3495, 3083, 1406, 401,
    -491, -1351, 610, -96, 125, -1895, -487, 415, 2, -166,
    -838, -1281, 1387, 2332, 27, -612, -680, 103, 1224, 1383,
    1353, 1104, 160, -813, -868, 0, 498, -45, 214, 1242,
    2089, 2094, 787, 55, -773, -511, 956, 2346, 1502, -929,
    -1029, 205, 664, 649, -1310, -1042, -21, -96, -1481, -1150,
    -208, -127, 189, -739, -1154, 1938, 258, -1957, -1349, -936,
    -653, -892, 414, 182, -480, -1372, -444, 422, 1411, 93,
    -665, -386, -1252, -600, 739, 1429, 599, -2190, -1192, 985,
    774, 744, -302, -953, -872, -1716, -103, 477, 1071, -766])
