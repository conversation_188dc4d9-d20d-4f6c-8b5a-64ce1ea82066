"""this script builds the T table and A table for the upper
   quantile stundentized range algorithm"""
from statsmodels.compat.python import lrange, lmap
import math
import scipy.stats
from scipy.optimize import leastsq

import numpy as np
from numpy.random import random

# The values for p in [.5, .75, .9, .95, .975, .99, .995, .999]
# were pulled from:
#    http://www.stata.com/stb/stb46/dm64/sturng.pdf
#
# Values for p in [.1, .675, .8, .85] were calculated using <PERSON>'s qtukey function
#
# the table was programmed by <PERSON><PERSON><PERSON> and extends <PERSON><PERSON>'s (1960) table
# using the Copenhaver & Holland (1988) algorithm (C&H). <PERSON><PERSON><PERSON> found
# that the 4th significant digit of the C&H differed from <PERSON><PERSON>'s
# tables on about 20% of the values. <PERSON><PERSON><PERSON> states this was do to
# consevative rounding by <PERSON><PERSON>. In those event the table reflects
# <PERSON><PERSON>'s orginal approximations.

q0100 = """\
2 0.2010022 0.6351172 0.9504689 1.179321 1.354691 1.495126 1.611354 1.709984 1.795325 1.87032 1.937057 1.997068 2.051505 2.101256 2.147016 2.189342 2.228683 2.265408 2.299823 2.558612 2.729718 2.95625 3.184742938 3.398609188
3 0.193179 0.6294481 0.9564746 1.19723 1.383028 1.532369 1.656225 1.761451 1.852559 1.93265 2.003933 2.068034 2.126178 2.179312 2.228177 2.273367 2.315364 2.354561 2.391287 2.667213 2.849389 3.009265469 3.237758406 3.451624656
4 0.1892648 0.6266441 0.9606115 1.2089 1.401557 1.55691 1.686009 1.795829 1.890994 1.974697 2.049222 2.116253 2.177065 2.232641 2.283754 2.331023 2.37495 2.415949 2.454361 2.742846 2.933173 3.062280938 3.290773875 3.504640125
5 0.1869239 0.6249713 0.963532 1.217021 1.414548 1.574255 1.707205 1.820437 1.91864 2.005066 2.082048 2.151312 2.214162 2.271609 2.324449 2.37332 2.418737 2.461128 2.500844 2.7991 2.9958 3.115296406 3.343789344 3.557655594
6 0.185369 0.6238602 0.9656833 1.22298 1.424151 1.587166 1.723076 1.838955 1.939532 2.028098 2.107021 2.178053 2.242524 2.301465 2.355686 2.40584 2.452454 2.495964 2.536731 2.842892 3.027993254 3.168311875 3.396804813 3.610671063
7 0.1842618 0.6230685 0.9673274 1.227534 1.431536 1.597154 1.735417 1.853415 1.955904 2.046203 2.126704 2.19918 2.264979 2.325144 2.380502 2.431713 2.479315 2.52375 2.565387 2.878126 3.060186557 3.221327344 3.449820281 3.663686531
8 0.1834338 0.6224757 0.9686225 1.231126 1.437392 1.605113 1.745294 1.86503 1.969097 2.060832 2.142645 2.216325 2.283234 2.344427 2.400739 2.45284 2.501275 2.546491 2.588864 2.861237 3.092379859 3.274342813 3.50283575 3.716702
9 0.1827912 0.6220153 0.969668 1.23403 1.442149 1.611608 1.753382 1.874572 1.979964 2.07291 2.155833 2.230535 2.298388 2.360458 2.417585 2.470448 2.519597 2.565484 2.608488 2.871492 3.1631665 3.362394 3.613696 3.7452106
10 0.1822783 0.6216474 0.9705293 1.236426 1.446091 1.617009 1.76013 1.882554 1.989077 2.083059 2.166935 2.242517 2.311185 2.374011 2.431845 2.485368 2.535137 2.581607 2.625162 2.898717 3.1760535 3.45339 3.6807265 3.7737192
11 0.1818593 0.6213467 0.9712507 1.238437 1.449411 1.621571 1.765847 1.889333 1.996831 2.091711 2.176415 2.252763 2.322141 2.38563 2.444082 2.498185 2.548497 2.59548 2.639519 2.923558 3.19971275 3.4758675 3.70202225 3.8022278
12 0.1815106 0.6210962 0.9718637 1.240149 1.452244 1.625478 1.770753 1.895163 2.003512 2.099178 2.184609 2.26163 2.331635 2.395707 2.454706 2.509321 2.560115 2.607554 2.652022 2.94649 3.2224175 3.498345 3.7242725 3.8307364
13 0.181216 0.6208845 0.9723908 1.241623 1.454692 1.62886 1.77501 1.900231 2.009331 2.10569 2.191763 2.269382 2.339943 2.404535 2.46402 2.519093 2.570318 2.618162 2.663015 2.967868 3.242456 3.517044 3.741632 3.859245
14 0.1809637 0.620703 0.972849 1.242906 1.456827 1.631817 1.778739 1.904678 2.014444 2.11142 2.198067 2.276219 2.347278 2.412335 2.472257 2.52774 2.579352 2.627563 2.672763 2.987963 3.245505708 3.503048417 3.748851667 3.878849
15 0.1807453 0.6205458 0.9732508 1.244033 1.458706 1.634424 1.782034 1.908613 2.018974 2.116503 2.203664 2.282296 2.353802 2.41928 2.479596 2.53545 2.587413 2.635954 2.681468 3.006982 3.275778125 3.489052833 3.758247778 3.896564
16 0.1805544 0.6204084 0.973606 1.24503 1.460373 1.636741 1.784965 1.912119 2.023015 2.121043 2.208668 2.287733 2.359646 2.425503 2.486177 2.542369 2.59465 2.643493 2.689292 3.025091 3.281966688 3.49479125 3.763291 3.9179256
17 0.1803861 0.6202871 0.9739223 1.245919 1.461861 1.638813 1.78759 1.915263 2.026643 2.125123 2.21317 2.292628 2.36491 2.431114 2.492114 2.548614 2.601186 2.650304 2.696365 3.032426 3.272395 3.50687125 3.77704 3.9392872
18 0.1802366 0.6201793 0.9742057 1.246717 1.463198 1.640677 1.789955 1.918099 2.029919 2.128809 2.21724 2.297059 2.369678 2.436199 2.497498 2.55428 2.607118 2.656491 2.702792 3.039093 3.282026 3.586406 3.789645 3.9606488
19 0.1801029 0.620083 0.9744612 1.247436 1.464405 1.642363 1.792096 1.920669 2.032891 2.132158 2.22094 2.301089 2.374017 2.44083 2.502404 2.559444 2.612529 2.662134 2.708658 3.045182 3.296680985 3.603883649 3.801248 3.974888443
20 0.1799827 0.6199962 0.9746925 1.248088 1.465502 1.643895 1.794045 1.923011 2.035601 2.135212 2.224318 2.30477 2.377983 2.445065 2.506892 2.564173 2.617485 2.667306 2.714034 3.050762 3.311335993 3.621361325 3.788047375 3.989130221
24 0.1796023 0.6197217 0.9754345 1.250184 1.469033 1.648844 1.800353 1.930604 2.044404 2.145153 2.235327 2.316784 2.390945 2.45892 2.521592 2.579673 2.633745 2.684289 2.731705 3.089271 3.325991 3.638839 3.77484675 4.003372
30 0.1792227 0.6194474 0.9761909 1.252326 1.47266 1.653946 1.806877 1.938484 2.053567 2.155527 2.246844 2.329381 2.404562 2.473504 2.537093 2.596046 2.650947 2.702281 2.750452 3.088623 3.365526818 3.632653344 3.810852875 4.0480485
40 0.1788439 0.6191733 0.976962 1.254517 1.476384 1.659208 1.813634 1.946672 2.063118 2.166372 2.258917 2.342618 2.418905 2.488898 2.553488 2.613394 2.669206 2.731569 2.770415 3.109261 3.405062637 3.629560516 3.846859 4.092725
60 0.1784658 0.6188994 0.9777482 1.256759 1.480212 1.664639 1.820636 1.955191 2.07309 2.177731 2.271599 2.356562 2.434053 2.505196 2.570884 2.631843 2.688663 2.745483 2.802303 3.159123 3.406221516 3.626467688 3.90118925 4.1497775
120 0.1780885 0.6186256 0.9785495 1.259052 1.484147 1.67025 1.827902 1.964066 2.083518 2.189653 2.284954 2.371292 2.450102 2.522511 2.589417 2.651546 2.71243375 2.763748 2.8156585 3.201588 3.459897 3.67055425 3.9555195 4.20683
1e38 0.177712 0.6183521 0.9793662 1.261398 1.488195 1.676051 1.835449 1.973327 2.094446 2.202195 2.299057 2.386902 2.467168 2.540983 2.609248 2.677513 2.745778 2.787396 2.829014 3.236691 3.487830797 3.721309063 4.01874075 4.279424"""

q0300 = """\
2 0.6289521 1.248281 1.638496 1.916298 2.129504 2.301246 2.444313 2.566465 2.672747 2.766604 2.850494 2.926224 2.995161 3.05836 3.116655 3.170712 3.221076 3.268192 3.312433 3.647666 3.871606 4.170521 4.372227 4.52341
3 0.5999117 1.209786 1.598235 1.875707 2.088948 2.260822 2.404037 2.52633 2.632732 2.726691 2.810665 2.886463 2.955453 3.018695 3.077022 3.131103 3.181483 3.228609 3.272854 3.607961 3.831649 4.130021 4.331231 4.48198
4 0.5857155 1.19124 1.579749 1.858059 2.072245 2.245015 2.389043 2.512062 2.619115 2.713659 2.798159 2.874434 2.943859 3.007497 3.066189 3.120607 3.171298 3.218713 3.263228 3.600301 3.825208 4.125075 4.327208 4.478602
5 0.5773226 1.18033 1.569213 1.84843 2.063579 2.237255 2.382108 2.505872 2.613597 2.708749 2.793803 2.870583 2.94047 3.004536 3.063622 3.118406 3.169439 3.217173 3.261987 3.601296 3.827646 4.129353 4.332665 4.48491
6 0.5717854 1.173145 1.562427 1.842442 2.058433 2.232905 2.378487 2.502915 2.611243 2.706943 2.792499 2.869739 2.940051 3.004509 3.06396 3.119085 3.170435 3.218468 3.263562 3.604991 3.832733 4.136241 4.340726 4.493824
7 0.5678608 1.168056 1.5577 1.838389 2.055092 2.230242 2.376451 2.501451 2.610302 2.706482 2.792477 2.870123 2.94081 3.005616 3.065391 3.120818 3.172464 3.220752 3.266098 3.609448 3.838467 4.143645 4.349225 4.503125
8 0.5649349 1.164263 1.55422 1.835477 2.052781 2.228508 2.375251 2.500743 2.610046 2.706641 2.793019 2.871019 2.942034 3.007146 3.067207 3.122901 3.174787 3.223323 3.268893 3.61396 3.844133 4.150832 4.357416 4.512054
9 0.56267 1.161326 1.551554 1.833289 2.051105 2.227323 2.374526 2.500442 2.610136 2.707092 2.793803 2.872112 2.943416 3.008796 3.069108 3.125039 3.177147 3.225893 3.27166 3.618262 3.849473 4.157551 4.365051 4.520364
10 0.5608651 1.158985 1.549445 1.831589 2.049842 2.226484 2.374084 2.500369 2.610403 2.707674 2.794677 2.873258 2.944815 3.010432 3.070966 3.127106 3.179411 3.228343 3.274287 3.62226 3.854408 4.16298725 4.372072 4.51335807
11 0.5593931 1.157076 1.547737 1.830233 2.048863 2.225872 2.373818 2.500424 2.610757 2.708305 2.795566 2.874386 2.946167 3.011994 3.072726 3.129052 3.181532 3.23063 3.276732 3.625936 3.8583805 4.1684235 4.378495 4.527370035
12 0.5581697 1.155489 1.546325 1.829125 2.048085 2.225415 2.373664 2.500554 2.61115 2.708943 2.796432 2.875466 2.947446 3.01346 3.074368 3.13086 3.183497 3.232744 3.278987 3.629302 3.862353 4.17385975 4.384365 4.541382
13 0.557137 1.154148 1.545138 1.828206 2.047454 2.225068 2.373584 2.500725 2.611555 2.709566 2.797258 2.876482 2.948641 3.014823 3.075889 3.13253 3.185309 3.23469 3.28106 3.63238 3.8663255 4.179296 4.3895105 4.547231
14 0.5562535 1.153001 1.544127 1.82743 2.046934 2.224798 2.373553 2.500918 2.611956 2.710162 2.798038 2.877433 2.949752 3.016085 3.077294 3.134069 3.186976 3.236478 3.282963 3.635197 3.870298 4.183625 4.394656 4.552594
15 0.5554892 1.152009 1.543255 1.826767 2.046499 2.224587 2.373555 2.50112 2.612347 2.710728 2.798768 2.878317 2.950781 3.017251 3.078589 3.135487 3.188509 3.238121 3.284712 3.637674 3.8733385 4.187597 4.399177 4.557525
16 0.5548214 1.151142 1.542495 1.826194 2.04613 2.224419 2.373579 2.501325 2.612721 2.711261 2.799449 2.879139 2.951735 3.018329 3.079784 3.136793 3.189921 3.239634 3.28632 3.640151 3.876379 4.191252 4.40334 4.56207
17 0.554233 1.150377 1.541828 1.825694 2.045813 2.224284 2.373618 2.501527 2.613077 2.711761 2.800085 2.879902 2.952618 3.019325 3.080888 3.137998 3.191223 3.241028 3.287802 3.642251 3.878959 4.1944965 4.407184 4.56627
18 0.5537107 1.149699 1.541236 1.825254 2.04554 2.224175 2.373667 2.501725 2.613414 2.71223 2.800678 2.880611 2.953437 3.020248 3.081909 3.139113 3.192426 3.242315 3.28917 3.644351 3.881539 4.197741 4.410743 4.570162
19 0.5532438 1.149092 1.540709 1.824864 2.045301 2.224086 2.373722 2.501915 2.613733 2.71267 2.801231 2.881271 2.954198 3.021104 3.082855 3.140145 3.19354 3.243507 3.290436 3.641213698 3.877674372 4.200632 4.414046 4.573776
20 0.552824 1.148546 1.540235 1.824516 2.045091 2.224013 2.37378 2.502099 2.614034 2.713082 2.801747 2.881885 2.954905 3.0219 3.083734 3.141103 3.194574 3.244613 3.291611 3.647488349 3.885403686 4.203319 4.4207865 4.577142
24 0.5514973 1.146821 1.538745 1.823433 2.044456 2.223827 2.374026 2.502754 2.615077 2.71449 2.8035 2.883965 2.957293 3.02458 3.086693 3.144327 3.198049 3.248329 3.295558 3.653763 3.893133 4.212396 4.427527 4.588561
30 0.5501747 1.1451 1.537267 1.822378 2.04387 2.223711 2.374365 2.503527 2.616262 2.716064 2.805443 2.886257 2.959917 3.027519 3.089932 3.147852 3.201849 3.252391 3.29987 3.646771403 3.901009 4.201241112 4.423813037 4.57425581
40 0.5488561 1.143384 1.535803 1.821351 2.043333 2.223667 2.3748 2.504422 2.617594 2.717812 2.807585 2.888774 2.962791 3.030734 3.093472 3.151703 3.205997 3.256824 3.304577 3.660754702 3.910106 4.223551056 4.431241019 4.602866405
60 0.5475416 1.141671 1.53435 1.820353 2.042846 2.223698 2.375337 2.505445 2.619082 2.719743 2.809939 2.891531 2.965932 3.034243 3.097332 3.1559 3.210518 3.261657 3.30971 3.674738 3.919203 4.245861 4.438669 4.631477
120 0.5462314 1.139963 1.532911 1.819385 2.04241 2.223806 2.375978 2.506602 2.620733 2.721868 2.812516 2.894541 2.969356 3.038064 3.101534 3.160468 3.215439 3.39400025 3.5725615 3.75112275 3.929684 4.256342 4.44915 4.641958
1e38 0.5449254 1.138259 1.531485 1.818447 2.042028 2.223993 2.376728 2.507898 2.622556 2.724195 2.815328 2.897817 2.973079 3.042215 3.106097 3.165428 3.220399 3.39896025 3.5775215 3.75608275 3.934644 4.261302 4.45411 4.646918"""

q0500 = """\
2 1.155 1.908 2.377 2.713 2.973 3.184 3.361 3.513 3.645 3.762 3.867 3.963 4.049 4.129 4.203 4.271 4.335 4.394 4.451 4.878 5.165 5.549 5.810 6.006
3 1.082 1.791 2.230 2.545 2.789 2.986 3.152 3.294 3.418 3.528 3.626 3.715 3.796 3.871 3.940 4.004 4.064 4.120 4.172 4.573 4.842 5.202 5.447 5.630
4 1.048 1.736 2.163 2.468 2.704 2.895 3.055 3.193 3.313 3.419 3.515 3.601 3.680 3.752 3.819 3.881 3.939 3.993 4.044 4.432 4.693 5.043 5.279 5.457
5 1.028 1.705 2.124 2.423 2.655 2.843 3.000 3.135 3.253 3.357 3.451 3.535 3.613 3.684 3.749 3.810 3.867 3.920 3.970 4.351 4.608 4.951 5.184 5.358
6 1.015 1.684 2.098 2.394 2.623 2.808 2.964 3.097 3.213 3.317 3.409 3.493 3.569 3.639 3.704 3.764 3.820 3.873 3.922 4.299 4.552 4.891 5.121 5.294
7 1.006 1.670 2.080 2.374 2.601 2.784 2.938 3.070 3.186 3.288 3.380 3.463 3.538 3.608 3.672 3.732 3.787 3.840 3.889 4.262 4.513 4.849 5.077 5.249
8 .9990 1.659 2.067 2.359 2.584 2.767 2.919 3.051 3.165 3.267 3.358 3.440 3.515 3.584 3.648 3.708 3.763 3.815 3.863 4.234 4.484 4.818 5.045 5.215
9 .9938 1.651 2.057 2.347 2.571 2.753 2.905 3.035 3.149 3.250 3.341 3.423 3.498 3.566 3.630 3.689 3.744 3.796 3.844 4.213 4.461 4.794 5.020 5.189
10 .9897 1.645 2.049 2.338 2.561 2.742 2.893 3.023 3.137 3.237 3.328 3.409 3.484 3.552 3.615 3.674 3.729 3.780 3.829 4.196 4.443 4.775 5.000 5.168
11 .9863 1.639 2.042 2.330 2.553 2.733 2.884 3.013 3.127 3.227 3.317 3.398 3.472 3.540 3.603 3.662 3.717 3.768 3.816 4.182 4.429 4.759 4.983 5.152
12 .9836 1.635 2.037 2.324 2.546 2.726 2.876 3.005 3.118 3.218 3.308 3.389 3.463 3.531 3.594 3.652 3.706 3.757 3.805 4.171 4.417 4.746 4.970 5.138
13 .9812 1.631 2.032 2.319 2.540 2.719 2.869 2.998 3.111 3.210 3.300 3.381 3.455 3.522 3.585 3.643 3.698 3.749 3.796 4.161 4.406 4.735 4.958 5.126
14 .9792 1.628 2.028 2.314 2.535 2.714 2.864 2.992 3.105 3.204 3.293 3.374 3.448 3.515 3.578 3.636 3.690 3.741 3.789 4.153 4.397 4.726 4.948 5.115
15 .9775 1.625 2.025 2.310 2.531 2.709 2.859 2.987 3.099 3.199 3.288 3.368 3.442 3.509 3.572 3.630 3.684 3.735 3.782 4.145 4.390 4.718 4.940 5.107
16 .9760 1.623 2.022 2.307 2.527 2.705 2.855 2.983 3.095 3.194 3.283 3.363 3.436 3.504 3.566 3.624 3.678 3.729 3.776 4.139 4.383 4.710 4.932 5.099
17 .9747 1.621 2.019 2.304 2.524 2.702 2.851 2.979 3.090 3.189 3.278 3.359 3.432 3.499 3.561 3.619 3.673 3.724 3.771 4.133 4.377 4.704 4.926 5.092
18 .9735 1.619 2.017 2.301 2.521 2.699 2.848 2.975 3.087 3.186 3.274 3.354 3.428 3.495 3.557 3.615 3.669 3.719 3.767 4.128 4.372 4.698 4.920 5.086
19 .9724 1.617 2.015 2.299 2.518 2.696 2.845 2.972 3.084 3.182 3.271 3.351 3.424 3.491 3.553 3.611 3.665 3.715 3.763 4.124 4.367 4.693 4.914 5.080
20 .9715 1.616 2.013 2.297 2.516 2.693 2.842 2.969 3.081 3.179 3.268 3.348 3.421 3.488 3.550 3.607 3.661 3.712 3.759 4.120 4.363 4.688 4.909 5.075
24 .9685 1.611 2.007 2.290 2.508 2.685 2.833 2.960 3.071 3.170 3.258 3.337 3.410 3.477 3.539 3.596 3.650 3.700 3.747 4.107 4.349 4.674 4.894 5.060
30 .9656 1.606 2.001 2.283 2.501 2.677 2.825 2.951 3.062 3.160 3.248 3.327 3.400 3.466 3.528 3.585 3.638 3.688 3.735 4.094 4.335 4.659 4.878 5.043
40 .9626 1.602 1.996 2.277 2.494 2.669 2.816 2.942 3.053 3.150 3.238 3.317 3.389 3.455 3.517 3.574 3.627 3.677 3.723 4.080 4.321 4.643 4.862 5.027
60 .9597 1.597 1.990 2.270 2.486 2.661 2.808 2.933 3.043 3.140 3.227 3.306 3.378 3.444 3.505 3.562 3.615 3.665 3.711 4.067 4.306 4.627 4.845 5.009
120 .9568 1.592 1.984 2.263 2.479 2.653 2.799 2.924 3.034 3.130 3.217 3.296 3.367 3.433 3.494 3.550 3.603 3.652 3.699 4.052 4.290 4.610 4.827 4.990
1e38 .9539 1.588 1.978 2.257 2.472 2.645 2.791 2.915 3.024 3.121 3.207 3.285 3.356 3.422 3.482 3.538 3.591 3.640 3.686 4.037 4.274 4.591 4.806 4.968"""

q0675 = """\
2 1.829602 2.751705 3.332700 3.754119 4.082579 4.350351 4.575528 4.769258 4.938876 5.089456 5.22465 5.347168 5.459072 5.56197 5.657136 5.745596 5.828188 5.905606 5.978428 6.534036 6.908522 7.411898 7.753537 8.010516
3 1.660743 2.469725 2.973973 3.338757 3.622958 3.854718 4.049715 4.217574 4.364624 4.495236 4.612559 4.718926 4.816117 4.905518 4.988228 5.065133 5.136955 5.204295 5.267653 5.751485 6.07799 6.517299 6.815682 7.040219
4 1.585479 2.344680 2.814410 3.153343 3.417165 3.632254 3.813232 3.96905 4.105579 4.226877 4.335857 4.434684 4.525004 4.608102 4.684995 4.756504 4.823298 4.885932 4.944872 5.395226 5.699385 6.108899 6.387203 6.596702
5 1.543029 2.27426 2.72431 3.048331 3.300303 3.505645 3.678397 3.827131 3.957462 4.073264 4.177319 4.27169 4.35795 4.437321 4.510774 4.579091 4.642911 4.702763 4.759089 5.189651 5.480611 5.872552 6.139026 6.339673
6 1.515809 2.229127 2.666435 2.980707 3.224876 3.423769 3.591058 3.735076 3.861273 3.973403 4.074164 4.165554 4.249094 4.325968 4.397115 4.463293 4.525119 4.583105 4.637678 5.054965 5.337079 5.717251 5.975813 6.170546
7 1.496881 2.197746 2.626119 2.933501 3.17212 3.366405 3.529778 3.670406 3.793624 3.903106 4.001488 4.090723 4.172295 4.247362 4.31684 4.381468 4.441849 4.498482 4.551786 4.959448 5.235148 5.606794 5.859629 6.050083
8 1.482962 2.174667 2.596423 2.898666 3.133126 3.323942 3.484357 3.622418 3.743376 3.850846 3.947419 4.035012 4.115085 4.188774 4.256978 4.320423 4.379701 4.435301 4.487633 4.887938 5.158734 5.523864 5.772325 5.959514
9 1.472298 2.156982 2.573637 2.871897 3.103116 3.29122 3.449316 3.585359 3.704538 3.810421 3.905564 3.991859 4.070746 4.143343 4.210539 4.273046 4.331448 4.386229 4.437792 4.832253 5.099153 5.459107 5.704096 5.888693
10 1.463868 2.142999 2.555601 2.85068 3.079300 3.265222 3.421445 3.555857 3.673594 3.778189 3.872171 3.957411 4.035332 4.107041 4.173413 4.235156 4.292845 4.346957 4.39789 4.787576 5.05129 5.407011 5.649161 5.831642
11 1.457037 2.131666 2.540969 2.833447 3.059936 3.244061 3.398740 3.531802 3.648345 3.751871 3.844888 3.929251 4.006369 4.077337 4.143024 4.204129 4.261223 4.314777 4.365186 4.750882 5.01193 5.36411 5.603886 5.784574
12 1.451389 2.122295 2.528860 2.819172 3.043878 3.226497 3.379879 3.511805 3.627341 3.729965 3.822167 3.905787 3.982224 4.052565 4.117671 4.178235 4.234823 4.287903 4.337867 4.720168 4.978946 5.32811 5.565864 5.745066
13 1.446642 2.114418 2.518673 2.807152 3.030346 3.211683 3.363958 3.494914 3.609588 3.71144 3.802942 3.885925 3.961777 4.031579 4.096185 4.156283 4.212436 4.265108 4.314686 4.694058 4.950875 5.297431 5.533435 5.711335
14 1.442597 2.107703 2.509984 2.796892 3.018785 3.199019 3.350338 3.480454 3.594382 3.695564 3.786459 3.868888 3.944232 4.013564 4.077734 4.137427 4.1932 4.245515 4.294759 4.671571 4.926672 5.270944 5.505418 5.682176
15 1.439108 2.101911 2.502485 2.788030 3.008793 3.188066 3.338551 3.467934 3.581209 3.681803 3.772166 3.854108 3.929005 3.997925 4.061712 4.121047 4.176486 4.228488 4.277436 4.651989 4.905573 5.247825 5.480945 5.656694
16 1.436068 2.096865 2.495948 2.7803 3.000071 3.178498 3.328250 3.456985 3.569684 3.669759 3.75965 3.841162 3.915663 3.984216 4.047663 4.106681 4.161824 4.213546 4.262231 4.634774 4.887015 5.227455 5.459365 5.634213
17 1.433397 2.092428 2.490198 2.773497 2.992391 3.170069 3.319169 3.447329 3.559514 3.659127 3.748598 3.829725 3.903873 3.972099 4.035242 4.093976 4.148852 4.200325 4.248775 4.619514 4.870529 5.209359 5.440181 5.614218
18 1.431030 2.088497 2.485101 2.767464 2.985576 3.162585 3.311102 3.438749 3.550474 3.649671 3.738765 3.819547 3.893377 3.961308 4.024178 4.082656 4.137293 4.188541 4.236778 4.605888 4.855804 5.193166 5.423003 5.596306
19 1.428918 2.08499 2.480552 2.762076 2.979488 3.155896 3.30389 3.431072 3.542383 3.641206 3.729960 3.810429 3.883971 3.951636 4.014258 4.072505 4.126925 4.177969 4.226014 4.593643 4.84256 5.178585 5.407524 5.580158
20 1.427023 2.081842 2.476467 2.757236 2.974016 3.149882 3.297401 3.424164 3.535099 3.633583 3.722027 3.802213 3.875493 3.942916 4.005312 4.063349 4.117571 4.168429 4.216298 4.582577 4.830579 5.16538 5.393498 5.565519
24 1.421053 2.071924 2.463589 2.741964 2.956732 3.130867 3.276871 3.402288 3.512015 3.609405 3.696852 3.776122 3.848556 3.915194 3.976858 4.03421 4.087789 4.138042 4.185340 4.547205 4.792208 5.122986 5.348394 5.518394
30 1.415131 2.062082 2.450796 2.726770 2.939512 3.111895 3.256356 3.380399 3.488888 3.585153 3.67157 3.749892 3.821449 3.887270 3.948172 4.00481 4.057717 4.107336 4.154034 4.511241 4.753052 5.079524 5.302021 5.469846
40 1.409257 2.052316 2.438086 2.711654 2.922351 3.092956 3.235846 3.358481 3.465697 3.5608 3.646150 3.723487 3.794128 3.859096 3.919198 3.975085 4.027284 4.076232 4.122296 4.474532 4.712898 5.034679 5.253982 5.419412
60 1.40343 2.042626 2.425459 2.696611 2.905244 3.074043 3.215327 3.336516 3.442417 3.536316 3.620555 3.696861 3.766541 3.830609 3.889866 3.944956 3.996401 4.044636 4.09002 4.436878 4.671454 4.987998 5.203693 5.366394
120 1.397651 2.033010 2.412913 2.681639 2.888185 3.055146 3.194785 3.314484 3.419022 3.511665 3.59474 3.66996 3.738623 3.801735 3.86009 3.914325 3.96496 4.012423 4.057072 4.398008 4.628308 4.938805 5.150236 5.309666
1e38 1.391918 2.023469 2.400447 2.666735 2.871167 3.036254 3.174203 3.292360 3.395479 3.486805 3.568651 3.642718 3.710296 3.772381 3.829761 3.883069 3.93282 3.979437 4.023276 4.357546 4.582861 4.886029 5.092081 5.247256"""

q0750 = """\
2 2.267583 3.308014 3.969236 4.451126 4.82785 5.13561 5.394819 5.618097 5.813776 5.987632 6.143829 6.285461 6.41489 6.533954 6.644113 6.746546 6.842214 6.931913 7.01631 7.660853 8.09584 8.68119 9.0788 9.377929
3 2.011896 2.883775 3.431223 3.829258 4.140443 4.394852 4.609323 4.794233 4.956425 5.10064 5.230299 5.347941 5.455509 5.554514 5.646158 5.73141 5.811064 5.885775 5.956093 6.493827 6.857365 7.3472 7.680302 7.931152
4 1.901267 2.701018 3.198596 3.559322 3.841087 4.071417 4.265624 4.433118 4.580085 4.710812 4.828384 4.935098 5.032703 5.122566 5.205771 5.283192 5.355547 5.423427 5.48733 5.976418 6.307462 6.753955 7.057827 7.286775
5 1.839820 2.599651 3.069171 3.40865 3.673526 3.889955 4.072422 4.229795 4.367901 4.490764 4.601285 4.701617 4.793402 4.877922 4.956192 5.029034 5.09712 5.161005 5.221154 5.681792 5.993844 6.415033 6.70187 6.918073
6 1.800788 2.535293 2.986795 3.312495 3.566338 3.773641 3.948369 4.099056 4.231292 4.348941 4.45478 4.550869 4.638783 4.719746 4.794731 4.864523 4.929764 4.990987 5.048635 5.490302 5.789693 6.194025 6.469523 6.677248
7 1.773818 2.490830 2.929770 3.245783 3.491823 3.692639 3.86185 4.007755 4.135786 4.249692 4.352165 4.445203 4.530329 4.608729 4.681345 4.748937 4.812126 4.871427 4.927269 5.35523 5.64547 6.037624 6.304935 6.506543
8 1.754075 2.458283 2.887956 3.196776 3.436989 3.632943 3.798002 3.940301 4.065154 4.176226 4.276148 4.366871 4.44988 4.526332 4.597147 4.663065 4.724692 4.782528 4.836995 5.254505 5.537762 5.920623 6.18169 6.378634
9 1.739001 2.433431 2.855984 3.159245 3.394934 3.587097 3.748912 3.888384 4.010742 4.119586 4.2175 4.306396 4.387735 4.462649 4.532041 4.596635 4.657026 4.713704 4.767081 5.176307 5.454025 5.829512 6.085625 6.27887
10 1.727116 2.413835 2.830746 3.129578 3.361648 3.550769 3.709972 3.847165 3.967506 4.074547 4.170832 4.258248 4.33823 4.411895 4.480128 4.543645 4.60303 4.658765 4.711254 5.113722 5.386914 5.756374 6.008439 6.198662
11 1.717506 2.397989 2.810315 3.105535 3.334641 3.521265 3.678317 3.813629 3.932304 4.037852 4.132788 4.218976 4.297831 4.370457 4.437728 4.500349 4.558895 4.613844 4.665594 5.062423 5.331834 5.696254 5.944934 6.13263
12 1.709576 2.384911 2.793439 3.085654 3.312288 3.496821 3.65207 3.785802 3.903075 4.007365 4.101163 4.186312 4.264215 4.335963 4.402419 4.46428 4.522117 4.5764 4.627523 5.019561 5.285754 5.645883 5.891679 6.077222
13 1.70292 2.373934 2.779263 3.068939 3.293478 3.476235 3.62995 3.762334 3.878409 3.981623 4.074447 4.158707 4.235793 4.306786 4.372542 4.433751 4.490977 4.544687 4.595269 4.983178 5.246593 5.603014 5.846316 6.029999
14 1.697255 2.364590 2.767188 3.05469 3.27743 3.458659 3.611050 3.742271 3.85731 3.959593 4.051572 4.13506 4.211437 4.281774 4.346922 4.407563 4.464258 4.517468 4.567581 4.951886 5.212872 5.566049 5.807169 5.989222
15 1.692374 2.356539 2.756778 3.042397 3.263576 3.443476 3.594714 3.724919 3.839053 3.940521 4.031759 4.114571 4.190326 4.260088 4.324701 4.384844 4.441071 4.493842 4.54354 4.92467 5.183511 5.533819 5.773008 5.95362
16 1.688126 2.349531 2.747712 3.031684 3.251494 3.430227 3.580451 3.709761 3.823097 3.923845 4.01443 4.096644 4.171848 4.2411 4.305239 4.364939 4.420752 4.473133 4.522464 4.900769 5.1577 5.505449 5.742915 5.92224
17 1.684395 2.343375 2.739744 3.022264 3.240865 3.418565 3.567889 3.696405 3.809031 3.909139 3.999142 4.080823 4.155535 4.224333 4.288048 4.347353 4.402795 4.454828 4.50383 4.879604 5.134819 5.48027 5.716186 5.894353
18 1.681092 2.337926 2.732687 3.013916 3.23144 3.408218 3.55674 3.684546 3.796536 3.896071 3.985552 4.066754 4.141026 4.209415 4.27275 4.331699 4.386808 4.438527 4.487232 4.860723 5.114389 5.457759 5.692272 5.86939
19 1.678147 2.333067 2.726393 3.006467 3.223026 3.398978 3.546777 3.673945 3.785363 3.884381 3.973391 4.054162 4.128035 4.196054 4.259045 4.317673 4.37248 4.423914 4.472351 4.843772 5.096029 5.437505 5.67074 5.846902
20 1.675506 2.328708 2.720745 2.999780 3.215469 3.390674 3.537821 3.664411 3.775312 3.873861 3.962444 4.042823 4.116334 4.184018 4.246696 4.305031 4.359563 4.410739 4.458932 4.828464 5.079434 5.419178 5.651242 5.82653
24 1.667194 2.314991 2.702957 2.978701 3.191627 3.364455 3.50952 3.63426 3.7435 3.840544 3.927753 4.006868 4.079211 4.14581 4.207477 4.264864 4.318505 4.368841 4.416241 4.779619 5.026378 5.360437 5.58865 5.761054
30 1.658964 2.301406 2.68532 2.957771 3.167919 3.338345 3.481298 3.604155 3.711699 3.8072 3.892996 3.970809 4.041947 4.107423 4.168039 4.224442 4.277156 4.326617 4.373187 4.730101 4.9724 5.300398 5.524486 5.693793
40 1.650814 2.28795 2.667830 2.936984 3.144337 3.312335 3.453142 3.574077 3.679882 3.773798 3.858136 3.934602 4.004488 4.068796 4.128318 4.183691 4.235434 4.283976 4.329675 4.679735 4.917252 5.238689 5.458277 5.62419
60 1.642744 2.274622 2.650486 2.916339 3.120874 3.286413 3.425034 3.544004 3.648023 3.740302 3.823131 3.898196 3.966776 4.029861 4.088234 4.142523 4.19324 4.24081 4.285584 4.628295 4.860604 5.174794 5.389348 5.551435
120 1.634753 2.261421 2.633285 2.895829 3.097525 3.260567 3.396959 3.513912 3.616089 3.706672 3.787929 3.861531 3.92874 3.990536 4.047692 4.10083 4.150455 4.196985 4.240768 4.575490 4.802013 5.107977 5.316696 5.474283
1e38 1.626840 2.248346 2.616224 2.875451 3.074279 3.234786 3.368898 3.483775 3.584045 3.672862 3.752475 3.824535 3.890294 3.950721 4.006580 4.058483 4.106932 4.152338 4.195044 4.520933 4.740866 5.037152 5.238766 5.390726"""

q0800 = """\
2 2.666345 3.820436 4.558532 5.098158 5.520848 5.866626 6.158145 6.409446 6.62982 6.825717 7.001791 7.161505 7.307502 7.441845 7.566171 7.681802 7.789818 7.891113 7.986436 8.714887 9.206808 9.868718 10.31830 10.65683
3 2.316120 3.245426 3.832597 4.261107 4.596942 4.871989 5.104169 5.304561 5.480484 5.637021 5.777843 5.905682 6.022626 6.130305 6.230013 6.322797 6.409513 6.49087 6.567462 7.153711 7.55053 8.085698 8.449862 8.724212
4 2.168283 3.003795 3.52645 3.90676 4.204595 4.44853 4.654519 4.832388 4.988615 5.127694 5.25287 5.366554 5.470593 5.566425 5.655195 5.737827 5.815079 5.887577 5.955847 6.47896 6.833568 7.31242 7.638648 7.884592
5 2.087215 2.871505 3.358337 3.711564 3.987876 4.214094 4.405111 4.57007 4.714986 4.844026 4.960193 5.065723 5.162321 5.25132 5.333779 5.410553 5.482342 5.549725 5.613191 6.099852 6.430105 6.876484 7.180827 7.410389
6 2.036122 2.788188 3.252203 3.588013 3.850385 4.06507 4.246305 4.402806 4.540296 4.662734 4.772969 4.873124 4.964814 5.049304 5.127595 5.200498 5.268677 5.33268 5.392969 5.855517 6.169658 6.594568 6.884463 7.103222
7 2.001005 2.730943 3.179141 3.502777 3.755348 3.961886 4.136188 4.286677 4.418877 4.536604 4.642603 4.738913 4.82709 4.908349 4.983652 5.05378 5.119368 5.180945 5.238953 5.684175 5.986732 6.39621 6.675724 6.886723
8 1.975395 2.689205 3.125785 3.440421 3.685706 3.886163 4.055272 4.201248 4.329470 4.443647 4.546448 4.639854 4.725375 4.804189 4.87723 4.945254 5.008879 5.068616 5.124894 5.556957 5.850708 6.248453 6.520077 6.725179
9 1.955898 2.657432 3.085114 3.392817 3.632464 3.828199 3.993263 4.135716 4.260824 4.37222 4.472512 4.563637 4.64707 4.723960 4.79522 4.861587 4.923664 4.981949 5.036862 5.458529 5.745314 6.133773 6.399153 6.59959
10 1.940561 2.632439 3.053086 3.355281 3.590431 3.782386 3.944205 4.083824 4.206424 4.315576 4.413841 4.503121 4.584862 4.660193 4.730009 4.795032 4.855852 4.912959 4.966762 5.37997 5.661078 6.041965 6.302252 6.498885
11 1.928182 2.612267 3.027211 3.324922 3.556399 3.745257 3.904411 4.041698 4.16223 4.269529 4.366119 4.453871 4.534212 4.608251 4.676869 4.740775 4.800552 4.85668 4.909561 5.315725 5.5921 5.966667 6.222702 6.41616
12 1.917981 2.595645 3.005872 3.299861 3.528278 3.714552 3.871475 4.006806 4.125603 4.231343 4.326522 4.412988 4.492148 4.565096 4.632701 4.695665 4.754559 4.809858 4.861959 5.262152 5.534506 5.9037 6.156119 6.346875
13 1.909431 2.581711 2.987972 3.278821 3.504650 3.688731 3.843759 3.977426 4.094742 4.199153 4.293127 4.378492 4.45664 4.528654 4.595391 4.657546 4.715682 4.770269 4.8217 5.216755 5.485643 5.850201 6.099497 6.287919
14 1.90216 2.569864 2.972742 3.260906 3.484516 3.666713 3.82011 3.952342 4.068380 4.171642 4.264573 4.348985 4.426256 4.497459 4.563444 4.624895 4.682373 4.736342 4.787189 5.177769 5.443632 5.804138 6.050703 6.237084
15 1.895903 2.559666 2.959626 3.245467 3.467154 3.647714 3.799691 3.930673 4.045596 4.147854 4.239872 4.32345 4.399954 4.470446 4.53577 4.596605 4.653505 4.706931 4.757265 5.143906 5.4071 5.764029 6.00818 6.192757
16 1.89046 2.550797 2.948212 3.232024 3.452027 3.631152 3.781882 3.911763 4.025705 4.127077 4.218291 4.301132 4.376957 4.446821 4.511561 4.571849 4.628237 4.681181 4.731062 5.114203 5.375025 5.728765 5.970765 6.153733
17 1.885683 2.543012 2.93819 3.220213 3.438729 3.616585 3.766210 3.895117 4.008187 4.108772 4.19927 4.281455 4.356676 4.42598 4.490198 4.549999 4.605930 4.658444 4.707919 5.08793 5.346623 5.697501 5.937567 6.119088
18 1.881457 2.536125 2.929319 3.209754 3.426947 3.603672 3.752313 3.880348 3.992639 4.09252 4.182377 4.263975 4.338653 4.407454 4.471204 4.530568 4.586089 4.638215 4.687324 5.064516 5.321288 5.66958 5.907896 6.088107
19 1.877691 2.529988 2.921412 3.200427 3.416435 3.592147 3.739903 3.867156 3.978745 4.077993 4.167272 4.24834 4.322529 4.390877 4.454204 4.513172 4.568321 4.620098 4.668877 5.043513 5.298541 5.644481 5.881205 6.060224
20 1.874315 2.524485 2.914320 3.192058 3.407000 3.581797 3.728755 3.855300 3.966255 4.064929 4.153685 4.234272 4.308018 4.375954 4.438897 4.497506 4.552317 4.603776 4.652254 5.024562 5.277998 5.621789 5.857057 6.034984
24 1.863701 2.507187 2.89201 3.165709 3.377268 3.549158 3.69357 3.817854 3.92678 4.023614 4.110690 4.189731 4.262048 4.328656 4.390359 4.447807 4.501527 4.551956 4.599461 4.964202 5.212441 5.549191 5.779675 5.954012
30 1.853207 2.490080 2.869925 3.139592 3.347756 3.516716 3.658554 3.780544 3.887403 3.982357 4.067712 4.145167 4.216014 4.281252 4.341675 4.397921 4.450509 4.49987 4.546362 4.903188 5.145946 5.47522 5.700597 5.871091
40 1.842829 2.473164 2.848060 3.113699 3.318456 3.484461 3.62369 3.743347 3.848096 3.941126 4.024712 4.100533 4.16986 4.233681 4.292775 4.34777 4.399178 4.447421 4.492854 4.841333 5.078248 5.399473 5.619306 5.785608
60 1.832568 2.456435 2.826413 3.088026 3.289358 3.452379 3.588962 3.70624 3.808829 3.899882 3.981645 4.055774 4.123524 4.185868 4.243575 4.297261 4.34743 4.394499 4.438814 4.778404 5.009002 5.321406 5.535087 5.696698
120 1.822478 2.439890 2.804980 3.062567 3.260456 3.420458 3.55435 3.669198 3.769570 3.858583 3.938458 4.010829 4.076934 4.137731 4.193979 4.246285 4.295145 4.340968 4.384094 4.714106 4.937761 5.24027 5.446912 5.603078
1e38 1.812388 2.423529 2.783758 3.037317 3.231739 3.388684 3.519834 3.632192 3.73028 3.817183 3.895093 3.965627 4.030005 4.089173 4.143877 4.194716 4.242179 4.286668 4.328517 4.648069 4.863937 5.155024 5.353283 5.50281"""

q0850 = """\
2 3.226562 4.548022 5.398759 6.022701 6.512387 6.913502 7.251997 7.54401 7.800236 8.028116 8.233021 8.418953 8.588968 8.74545 8.890294 9.02503 9.150913 9.268977 9.380094 10.22972 10.80450 11.58094 12.11086 12.51097
3 2.721399 3.731515 4.374509 4.845675 5.215912 5.5197 5.776502 5.998388 6.193356 6.366968 6.523249 6.665198 6.795111 6.914781 7.025634 7.128823 7.225292 7.315823 7.401073 8.054202 8.496827 9.094477 9.501702 9.808753
4 2.514747 3.399285 3.956491 4.363675 4.68348 4.945965 5.16798 5.359938 5.52872 5.679113 5.814574 5.937683 6.050411 6.154302 6.25058 6.34024 6.424092 6.502812 6.576963 7.145835 7.532079 8.054293 8.410406 8.679063
5 2.403262 3.220436 3.730867 4.102766 4.394545 4.633955 4.836465 5.011596 5.165628 5.302922 5.426626 5.539086 5.642096 5.737057 5.825086 5.907085 5.983793 6.055822 6.123687 6.644817 6.999123 7.478735 7.806159 8.05333
6 2.333697 3.108965 3.589945 3.939419 4.213263 4.437836 4.627757 4.791998 4.936465 5.06525 5.181307 5.286833 5.38351 5.47265 5.555297 5.632296 5.704339 5.771998 5.835756 6.325681 6.659107 7.110867 7.419518 7.652631
7 2.286206 3.032919 3.493643 3.827573 4.088914 4.303095 4.484169 4.640738 4.77845 4.901217 5.011858 5.112468 5.20465 5.289655 5.368478 5.441922 5.510646 5.575196 5.63603 6.103717 6.422253 6.85415 7.149428 7.372543
8 2.251741 2.977758 3.423692 3.746199 3.998303 4.204778 4.379271 4.530117 4.662781 4.781044 4.887624 4.984545 5.073351 5.155248 5.231193 5.30196 5.368185 5.430392 5.489022 5.939926 6.24721 6.664096 6.949269 7.16483
9 2.225598 2.935932 3.370588 3.684337 3.92933 4.12985 4.299243 4.445643 4.57438 4.68913 4.792542 4.88658 4.972745 5.052208 5.125899 5.194568 5.258832 5.319201 5.376101 5.81381 6.112237 6.517297 6.794508 7.004117
10 2.205093 2.903132 3.328904 3.635722 3.875064 4.070838 4.236155 4.378996 4.504581 4.61651 4.717372 4.809088 4.893124 4.970624 5.042494 5.109469 5.172148 5.231029 5.28653 5.713546 6.004781 6.400234 6.670974 6.875744
11 2.18858 2.876725 3.295316 3.596509 3.831250 4.023149 4.18513 4.325052 4.448048 4.557656 4.656418 4.74622 4.828499 4.904377 4.974743 5.040315 5.101683 5.159333 5.213674 5.631818 5.917077 6.304535 6.569888 6.770631
12 2.174999 2.855009 3.267675 3.564211 3.795132 3.983803 4.143002 4.280484 4.401312 4.508975 4.605974 4.694168 4.77497 4.849483 4.918582 4.982973 5.043235 5.099847 5.153209 5.563854 5.84405 6.22473 6.485512 6.682838
13 2.163633 2.836837 3.244531 3.537147 3.764842 3.950784 4.107624 4.243034 4.36202 4.468027 4.563524 4.650346 4.729887 4.803233 4.871249 4.93463 4.993946 5.049668 5.102191 5.5064 5.782244 6.157087 6.413931 6.608311
14 2.153982 2.821408 3.224869 3.514139 3.739075 3.922677 4.077491 4.21112 4.328519 4.433097 4.527298 4.612934 4.691385 4.763723 4.830801 4.893306 4.951801 5.006752 5.058548 5.457164 5.729217 6.09897 6.352377 6.544185
15 2.145684 2.808145 3.207959 3.494339 3.716887 3.898461 4.051515 4.183594 4.299611 4.402944 4.496014 4.580615 4.658112 4.729568 4.795825 4.857564 4.91534 4.969615 5.020773 5.414476 5.683193 6.048461 6.298836 6.488374
16 2.138475 2.796621 3.193261 3.47712 3.697581 3.877377 4.028889 4.159607 4.274409 4.376647 4.468721 4.55241 4.629066 4.699743 4.765275 4.826337 4.883478 4.937154 4.987748 5.377097 5.642851 6.004129 6.251805 6.439321
17 2.132153 2.786517 3.180367 3.462007 3.680628 3.858856 4.009003 4.138517 4.252242 4.353508 4.444698 4.527576 4.603485 4.673469 4.738356 4.798814 4.855389 4.908532 4.958622 5.344083 5.607184 5.964886 6.21014 6.395841
18 2.126564 2.777584 3.168965 3.448637 3.665623 3.842455 3.991387 4.119827 4.232591 4.332989 4.423388 4.505541 4.58078 4.650143 4.714452 4.774369 4.830436 4.883101 4.932739 5.314701 5.575413 5.929888 6.172953 6.357013
19 2.121587 2.769631 3.158810 3.436724 3.652248 3.82783 3.975673 4.103149 4.21505 4.314667 4.404354 4.485854 4.560491 4.629294 4.693081 4.752509 4.808118 4.860351 4.909581 5.288379 5.546924 5.898469 6.139544 6.322112
20 2.117128 2.762505 3.149708 3.426043 3.640251 3.814707 3.961568 4.088173 4.199295 4.298206 4.387249 4.468158 4.542248 4.610545 4.673858 4.732844 4.788036 4.839877 4.888736 5.264656 5.521227 5.870097 6.109355 6.290558
24 2.103128 2.740133 3.121118 3.392466 3.60251 3.773393 3.917129 4.040961 4.149593 4.246248 4.33323 4.412242 4.484578 4.551244 4.613036 4.670595 4.724445 4.775019 4.822681 5.189274 5.439419 5.779554 6.012859 6.189586
30 2.089309 2.718054 3.092876 3.359261 3.56514 3.732436 3.873024 3.994053 4.10016 4.19452 4.2794 4.356475 4.427015 4.492009 4.552236 4.608325 4.660792 4.710058 4.756481 5.113372 5.356776 5.687685 5.914664 6.08662
40 2.07567 2.696264 3.064978 3.326418 3.52813 3.691822 3.829233 3.947423 4.050965 4.142986 4.225718 4.300807 4.369502 4.432773 4.491385 4.545955 4.596987 4.644896 4.69003 5.036754 5.27302 5.594062 5.814221 5.981005
60 2.062208 2.674759 3.037417 3.293931 3.491470 3.651535 3.785736 3.901046 4.001976 4.091607 4.172136 4.245183 4.311975 4.373463 4.430401 4.483391 4.532928 4.579419 4.623205 4.95919 5.187807 5.498134 5.710792 5.871841
120 2.048920 2.653534 3.010189 3.261791 3.455148 3.611561 3.742514 3.854896 3.953159 4.040341 4.118605 4.189543 4.254363 4.314 4.369191 4.42053 4.4685 4.513501 4.555864 4.880396 5.100706 5.399172 5.60337 5.757859
1e38 2.035805 2.632586 2.983286 3.229990 3.419154 3.571884 3.699544 3.808945 3.904479 3.989143 4.065068 4.133821 4.19659 4.254292 4.307653 4.357255 4.403572 4.446994 4.487848 4.800043 5.011193 5.296241 5.4906 5.637297"""

q0900 = """\
1 8.929 13.44 16.36 18.49 20.15 21.51 22.64 23.62 24.48 25.24 25.92 26.54 27.10 27.62 28.10 28.54 28.96 29.35 29.71 32.50 34.38 36.91 38.62 39.91
2 4.129 5.733 6.773 7.538 8.139 8.633 9.049 9.409 9.725 10.01 10.26 10.49 10.70 10.89 11.07 11.24 11.39 11.54 11.68 12.73 13.44 14.40 15.04 15.54
3 3.328 4.467 5.199 5.738 6.162 6.511 6.806 7.062 7.287 7.487 7.667 7.831 7.982 8.120 8.248 8.368 8.479 8.584 8.683 9.440 9.954 10.65 11.12 11.48
4 3.015 3.976 4.586 5.035 5.388 5.679 5.926 6.139 6.327 6.494 6.645 6.783 6.909 7.025 7.132 7.233 7.326 7.414 7.497 8.135 8.569 9.156 9.558 9.861
5 2.850 3.717 4.264 4.664 4.979 5.238 5.458 5.648 5.816 5.965 6.100 6.223 6.336 6.439 6.536 6.626 6.710 6.788 6.863 7.435 7.824 8.353 8.714 8.987
6 2.748 3.558 4.065 4.435 4.726 4.966 5.168 5.344 5.499 5.637 5.762 5.875 5.979 6.075 6.164 6.247 6.325 6.398 6.466 6.996 7.358 7.848 8.184 8.438
7 2.679 3.451 3.931 4.280 4.555 4.780 4.971 5.137 5.283 5.413 5.530 5.637 5.735 5.826 5.910 5.988 6.061 6.130 6.195 6.695 7.036 7.500 7.818 8.059
8 2.630 3.374 3.834 4.169 4.431 4.646 4.829 4.987 5.126 5.250 5.362 5.464 5.558 5.644 5.724 5.799 5.869 5.935 5.997 6.475 6.801 7.245 7.550 7.780
9 2.592 3.316 3.761 4.084 4.337 4.545 4.721 4.873 5.007 5.126 5.234 5.333 5.423 5.506 5.583 5.655 5.722 5.786 5.845 6.306 6.621 7.049 7.343 7.566
10 2.563 3.270 3.704 4.018 4.264 4.465 4.636 4.783 4.913 5.029 5.134 5.229 5.316 5.397 5.472 5.542 5.607 5.668 5.726 6.173 6.478 6.894 7.180 7.396
11 2.540 3.234 3.658 3.965 4.205 4.401 4.567 4.711 4.838 4.951 5.053 5.145 5.231 5.309 5.382 5.450 5.514 5.573 5.630 6.065 6.363 6.768 7.046 7.257
12 2.521 3.204 3.621 3.921 4.156 4.349 4.511 4.652 4.776 4.886 4.986 5.076 5.160 5.236 5.308 5.374 5.436 5.495 5.550 5.975 6.267 6.663 6.936 7.142
13 2.504 3.179 3.589 3.885 4.116 4.304 4.464 4.602 4.724 4.832 4.930 5.019 5.100 5.175 5.245 5.310 5.371 5.429 5.483 5.900 6.186 6.575 6.842 7.045
14 2.491 3.158 3.563 3.854 4.081 4.267 4.424 4.560 4.679 4.786 4.882 4.969 5.050 5.124 5.192 5.256 5.316 5.372 5.426 5.836 6.116 6.499 6.762 6.961
15 2.479 3.140 3.540 3.828 4.052 4.235 4.390 4.524 4.641 4.746 4.841 4.927 5.006 5.079 5.146 5.209 5.268 5.324 5.376 5.780 6.056 6.433 6.692 6.888
16 2.469 3.124 3.520 3.804 4.026 4.207 4.360 4.492 4.608 4.712 4.805 4.890 4.968 5.040 5.106 5.169 5.227 5.282 5.333 5.731 6.004 6.376 6.631 6.825
17 2.460 3.110 3.503 3.784 4.003 4.182 4.334 4.464 4.579 4.681 4.774 4.857 4.934 5.005 5.071 5.133 5.190 5.244 5.295 5.688 5.958 6.325 6.577 6.769
18 2.452 3.098 3.487 3.766 3.984 4.161 4.310 4.440 4.553 4.654 4.746 4.829 4.905 4.975 5.040 5.101 5.158 5.211 5.262 5.650 5.917 6.280 6.529 6.718
19 2.445 3.087 3.474 3.751 3.966 4.142 4.290 4.418 4.530 4.630 4.721 4.803 4.878 4.948 5.012 5.072 5.129 5.182 5.232 5.616 5.880 6.239 6.486 6.673
20 2.439 3.077 3.462 3.736 3.950 4.124 4.271 4.398 4.510 4.609 4.699 4.780 4.855 4.923 4.987 5.047 5.103 5.155 5.205 5.586 5.847 6.202 6.447 6.633
24 2.420 3.047 3.423 3.692 3.900 4.070 4.213 4.336 4.445 4.541 4.628 4.707 4.780 4.847 4.909 4.966 5.020 5.071 5.119 5.489 5.741 6.086 6.323 6.503
30 2.400 3.017 3.386 3.648 3.851 4.016 4.155 4.275 4.381 4.474 4.559 4.635 4.706 4.770 4.830 4.886 4.939 4.988 5.034 5.391 5.636 5.969 6.198 6.372
40 2.381 2.988 3.348 3.605 3.802 3.963 4.099 4.215 4.317 4.408 4.490 4.564 4.632 4.694 4.752 4.806 4.857 4.904 4.949 5.294 5.529 5.850 6.071 6.238
60 2.363 2.959 3.312 3.562 3.755 3.911 4.042 4.155 4.254 4.342 4.421 4.493 4.558 4.619 4.675 4.727 4.775 4.821 4.864 5.196 5.422 5.730 5.941 6.101
120 2.344 2.930 3.276 3.520 3.707 3.859 3.986 4.096 4.191 4.276 4.353 4.422 4.485 4.543 4.597 4.647 4.694 4.738 4.779 5.097 5.313 5.606 5.808 5.960
1e38 2.326 2.902 3.240 3.478 3.661 3.808 3.931 4.037 4.129 4.211 4.285 4.351 4.412 4.468 4.519 4.568 4.612 4.654 4.694 4.997 5.202 5.480 5.669 5.812"""

q0950 = """\
1 17.97 26.98 32.82 37.08 40.41 43.12 45.40 47.36 49.07 50.59 51.96 53.20 54.33 55.36 56.32 57.22 58.04 58.83 59.56 65.15 68.92 73.97 77.40 79.98
2 6.085 8.331 9.799 10.88 11.73 12.43 13.03 13.54 13.99 14.40 14.76 15.09 15.39 15.65 15.92 16.14 16.38 16.57 16.78 18.27 19.28 20.66 21.59 22.29
3 4.501 5.910 6.825 7.502 8.037 8.478 8.852 9.177 9.462 9.717 9.946 10.15 10.35 10.52 10.69 10.84 10.98 11.11 11.24 12.21 12.86 13.76 14.36 14.82
4 3.926 5.040 5.757 6.287 6.706 7.053 7.347 7.602 7.826 8.027 8.208 8.373 8.524 8.664 8.793 8.914 9.027 9.133 9.233 10.00 10.53 11.24 11.73 12.10
5 3.635 4.602 5.218 5.673 6.033 6.330 6.582 6.801 6.995 7.167 7.323 7.466 7.596 7.716 7.828 7.932 8.030 8.122 8.208 8.875 9.330 9.949 10.37 10.69
6 3.460 4.339 4.896 5.305 5.628 5.895 6.122 6.319 6.493 6.649 6.789 6.917 7.034 7.143 7.244 7.338 7.426 7.508 7.586 8.189 8.601 9.162 9.547 9.839
7 3.344 4.165 4.681 5.060 5.359 5.606 5.815 5.997 6.158 6.302 6.431 6.550 6.658 6.759 6.852 6.939 7.020 7.097 7.169 7.727 8.110 8.631 8.989 9.260
8 3.261 4.041 4.529 4.886 5.167 5.399 5.596 5.767 5.918 6.053 6.175 6.287 6.389 6.483 6.571 6.653 6.729 6.801 6.869 7.395 7.756 8.248 8.586 8.843
9 3.199 3.948 4.415 4.755 5.024 5.244 5.432 5.595 5.738 5.867 5.983 6.089 6.186 6.276 6.359 6.437 6.510 6.579 6.643 7.144 7.488 7.958 8.281 8.526
10 3.151 3.877 4.327 4.654 4.912 5.124 5.304 5.460 5.598 5.722 5.833 5.935 6.028 6.114 6.194 6.269 6.339 6.405 6.467 6.948 7.278 7.730 8.041 8.276
11 3.113 3.820 4.256 4.574 4.823 5.028 5.202 5.353 5.486 5.605 5.713 5.811 5.901 5.984 6.062 6.134 6.202 6.265 6.325 6.790 7.109 7.546 7.847 8.075
12 3.081 3.773 4.199 4.508 4.750 4.950 5.119 5.265 5.395 5.510 5.615 5.710 5.797 5.878 5.953 6.023 6.089 6.151 6.209 6.660 6.970 7.394 7.687 7.908
13 3.055 3.734 4.151 4.453 4.690 4.884 5.049 5.192 5.318 5.431 5.533 5.625 5.711 5.789 5.862 5.931 5.995 6.055 6.112 6.551 6.853 7.267 7.552 7.769
14 3.033 3.701 4.111 4.407 4.639 4.829 4.990 5.130 5.253 5.364 5.463 5.554 5.637 5.714 5.785 5.852 5.915 5.973 6.029 6.459 6.754 7.159 7.437 7.649
15 3.014 3.673 4.076 4.367 4.595 4.782 4.940 5.077 5.198 5.306 5.403 5.492 5.574 5.649 5.719 5.785 5.846 5.904 5.958 6.379 6.669 7.065 7.338 7.546
16 2.998 3.649 4.046 4.333 4.557 4.741 4.896 5.031 5.150 5.256 5.352 5.439 5.519 5.593 5.662 5.726 5.786 5.843 5.896 6.310 6.594 6.983 7.252 7.456
17 2.984 3.628 4.020 4.303 4.524 4.705 4.858 4.991 5.108 5.212 5.306 5.392 5.471 5.544 5.612 5.675 5.734 5.790 5.842 6.249 6.529 6.912 7.176 7.377
18 2.971 3.609 3.997 4.276 4.494 4.673 4.824 4.955 5.071 5.173 5.266 5.351 5.429 5.501 5.567 5.629 5.688 5.743 5.794 6.195 6.471 6.848 7.108 7.307
19 2.960 3.593 3.977 4.253 4.468 4.645 4.794 4.924 5.037 5.139 5.231 5.314 5.391 5.462 5.528 5.589 5.647 5.701 5.752 6.147 6.419 6.791 7.048 7.244
20 2.950 3.578 3.958 4.232 4.445 4.620 4.768 4.895 5.008 5.108 5.199 5.282 5.357 5.427 5.492 5.553 5.610 5.663 5.714 6.104 6.372 6.740 6.994 7.187
24 2.919 3.532 3.901 4.166 4.373 4.541 4.684 4.807 4.915 5.012 5.099 5.179 5.251 5.319 5.381 5.439 5.494 5.545 5.594 5.968 6.226 6.578 6.822 7.007
30 2.888 3.486 3.845 4.102 4.301 4.464 4.601 4.720 4.824 4.917 5.001 5.077 5.147 5.211 5.271 5.327 5.379 5.429 5.475 5.833 6.080 6.417 6.650 6.827
40 2.858 3.442 3.791 4.039 4.232 4.388 4.521 4.634 4.735 4.824 4.904 4.977 5.044 5.106 5.163 5.216 5.266 5.313 5.358 5.700 5.934 6.255 6.477 6.645
60 2.829 3.399 3.737 3.977 4.163 4.314 4.441 4.550 4.646 4.732 4.808 4.878 4.942 5.001 5.056 5.107 5.154 5.199 5.241 5.566 5.789 6.093 6.302 6.462
120 2.800 3.356 3.685 3.917 4.096 4.241 4.363 4.468 4.560 4.641 4.714 4.781 4.842 4.898 4.950 4.998 5.043 5.086 5.126 5.434 5.644 5.929 6.126 6.275
1e38 2.772 3.314 3.633 3.858 4.030 4.170 4.286 4.387 4.474 4.552 4.622 4.685 4.743 4.796 4.845 4.891 4.934 4.974 5.012 5.301 5.498 5.764 5.947 6.085"""

q0975 = """\
1 35.99 54.00 65.69 74.22 80.87 86.29 90.85 94.77 98.20 101.3 104.0 106.5 108.8 110.8 112.7 114.5 116.2 117.7 119.2 130.4 137.9 148.1 154.9 160.0
2 8.776 11.94 14.02 15.54 16.75 17.74 18.58 19.31 19.95 20.52 21.03 21.49 21.91 22.30 22.67 23.01 23.32 23.62 23.89 26.03 27.47 29.42 30.74 31.74
3 5.907 7.661 8.808 9.659 10.33 10.89 11.36 11.77 12.14 12.46 12.75 13.01 13.25 13.47 13.68 13.87 14.05 14.22 14.38 15.62 16.46 17.58 18.37 18.95
4 4.943 6.244 7.088 7.715 8.213 8.625 8.975 9.279 9.548 9.788 10.00 10.20 10.38 10.55 10.71 10.85 10.99 11.11 11.23 12.16 12.78 13.65 14.23 14.68
5 4.474 5.558 6.257 6.775 7.186 7.526 7.816 8.068 8.291 8.490 8.670 8.834 8.984 9.124 9.253 9.373 9.486 9.592 9.693 10.47 11.00 11.72 12.21 12.59
6 4.198 5.158 5.772 6.226 6.586 6.884 7.138 7.359 7.554 7.729 7.887 8.031 8.163 8.285 8.399 8.505 8.605 8.698 8.787 9.469 9.937 10.57 11.01 11.34
7 4.018 4.897 5.455 5.867 6.194 6.464 6.694 6.894 7.071 7.230 7.373 7.504 7.624 7.735 7.838 7.935 8.025 8.110 8.191 8.812 9.239 9.822 10.22 10.53
8 3.891 4.714 5.233 5.616 5.919 6.169 6.382 6.567 6.731 6.878 7.011 7.132 7.244 7.347 7.442 7.532 7.616 7.694 7.769 8.346 8.743 9.286 9.660 9.944
9 3.797 4.578 5.069 5.430 5.715 5.950 6.151 6.325 6.479 6.617 6.742 6.856 6.961 7.057 7.148 7.232 7.311 7.385 7.455 7.999 8.373 8.885 9.238 9.506
10 3.725 4.474 4.943 5.286 5.558 5.782 5.972 6.138 6.284 6.415 6.534 6.642 6.742 6.834 6.920 7.000 7.075 7.145 7.212 7.729 8.085 8.574 8.910 9.166
11 3.667 4.391 4.843 5.173 5.433 5.648 5.830 5.989 6.130 6.255 6.369 6.473 6.568 6.656 6.738 6.815 6.887 6.955 7.018 7.514 7.856 8.324 8.648 8.894
12 3.620 4.324 4.761 5.080 5.332 5.539 5.715 5.868 6.004 6.125 6.235 6.335 6.426 6.511 6.591 6.664 6.734 6.799 6.860 7.338 7.668 8.120 8.433 8.670
13 3.582 4.269 4.694 5.004 5.248 5.449 5.620 5.768 5.899 6.017 6.123 6.220 6.309 6.391 6.468 6.539 6.606 6.670 6.729 7.192 7.511 7.950 8.253 8.484
14 3.549 4.222 4.638 4.940 5.178 5.373 5.540 5.684 5.812 5.926 6.029 6.123 6.210 6.290 6.364 6.434 6.499 6.560 6.618 7.068 7.379 7.806 8.100 8.325
15 3.521 4.182 4.589 4.885 5.117 5.309 5.471 5.612 5.736 5.848 5.948 6.040 6.125 6.205 6.275 6.343 6.407 6.467 6.523 6.962 7.265 7.682 7.969 8.189
16 3.497 4.148 4.548 4.838 5.065 5.253 5.412 5.550 5.671 5.780 5.879 5.969 6.051 6.128 6.199 6.265 6.327 6.386 6.441 6.870 7.167 7.574 7.856 8.070
17 3.476 4.118 4.511 4.797 5.020 5.204 5.360 5.495 5.615 5.722 5.818 5.906 5.987 6.062 6.132 6.197 6.258 6.315 6.369 6.790 7.080 7.479 7.756 7.966
18 3.458 4.091 4.479 4.760 4.980 5.161 5.315 5.448 5.565 5.670 5.765 5.851 5.931 6.004 6.073 6.137 6.196 6.253 6.306 6.719 7.004 7.396 7.667 7.874
19 3.441 4.068 4.451 4.728 4.945 5.123 5.274 5.405 5.521 5.624 5.717 5.803 5.881 5.953 6.020 6.083 6.142 6.197 6.250 6.656 6.936 7.322 7.589 7.792
20 3.427 4.047 4.426 4.699 4.914 5.089 5.238 5.367 5.481 5.583 5.675 5.759 5.836 5.907 5.974 6.035 6.093 6.148 6.199 6.599 6.876 7.255 7.518 7.718
24 3.381 3.982 4.347 4.610 4.816 4.984 5.126 5.250 5.358 5.455 5.543 5.623 5.696 5.764 5.827 5.886 5.941 5.993 6.042 6.422 6.685 7.046 7.295 7.486
30 3.337 3.919 4.271 4.523 4.720 4.881 5.017 5.134 5.238 5.330 5.414 5.490 5.560 5.624 5.684 5.740 5.792 5.841 5.888 6.248 6.497 6.838 7.075 7.255
40 3.294 3.858 4.196 4.439 4.627 4.780 4.910 5.022 5.120 5.208 5.287 5.360 5.426 5.487 5.543 5.596 5.646 5.692 5.736 6.077 6.311 6.633 6.855 7.025
60 3.251 3.798 4.124 4.356 4.536 4.682 4.806 4.912 5.006 5.089 5.164 5.232 5.295 5.352 5.406 5.456 5.502 5.546 5.588 5.908 6.127 6.428 6.636 6.795
120 3.210 3.739 4.053 4.275 4.447 4.587 4.704 4.805 4.894 4.972 5.043 5.107 5.166 5.221 5.271 5.318 5.362 5.403 5.442 5.741 5.946 6.225 6.418 6.564
1e38 3.170 3.682 3.984 4.197 4.361 4.494 4.605 4.700 4.784 4.858 4.925 4.985 5.041 5.092 5.139 5.183 5.224 5.262 5.299 5.577 5.766 6.023 6.199 6.333"""

q0990 = """\
1 90.02 135.0 164.3 185.6 202.2 215.8 227.2 237.0 245.6 253.2 260.0 266.2 271.8 277.0 281.8 286.3 290.4 294.3 298.0 326.0 344.8 370.1 387.3 400.1
2 14.04 19.02 22.29 24.72 26.63 28.20 29.53 30.68 31.69 32.59 33.40 34.13 34.81 35.43 36.00 36.53 37.03 37.50 37.95 41.32 43.61 46.70 48.80 50.38
3 8.260 10.62 12.17 13.32 14.24 15.00 15.65 16.21 16.69 17.13 17.53 17.89 18.22 18.52 18.81 19.07 19.32 19.55 19.77 21.44 22.59 24.13 25.19 25.99
4 6.511 8.120 9.173 9.958 10.58 11.10 11.54 11.92 12.26 12.57 12.84 13.09 13.32 13.53 13.72 13.91 14.08 14.24 14.39 15.57 16.38 17.46 18.20 18.77
5 5.702 6.976 7.804 8.421 8.913 9.321 9.669 9.971 10.24 10.48 10.70 10.89 11.08 11.24 11.40 11.55 11.68 11.81 11.93 12.87 13.51 14.39 14.99 15.45
6 5.243 6.331 7.033 7.556 7.972 8.318 8.612 8.869 9.097 9.300 9.485 9.653 9.808 9.951 10.08 10.21 10.32 10.43 10.54 11.34 11.89 12.65 13.17 13.55
7 4.949 5.919 6.542 7.005 7.373 7.678 7.939 8.166 8.367 8.548 8.711 8.860 8.997 9.124 9.242 9.353 9.456 9.553 9.645 10.36 10.85 11.52 11.98 12.34
8 4.745 5.635 6.204 6.625 6.959 7.237 7.474 7.680 7.863 8.027 8.176 8.311 8.436 8.552 8.659 8.760 8.854 8.943 9.027 9.677 10.13 10.74 11.17 11.49
9 4.596 5.428 5.957 6.347 6.657 6.915 7.134 7.325 7.494 7.646 7.784 7.910 8.025 8.132 8.232 8.325 8.412 8.495 8.573 9.177 9.594 10.17 10.56 10.86
10 4.482 5.270 5.769 6.136 6.428 6.669 6.875 7.054 7.213 7.356 7.485 7.603 7.712 7.812 7.906 7.993 8.075 8.153 8.226 8.794 9.186 9.726 10.10 10.38
11 4.392 5.146 5.621 5.970 6.247 6.476 6.671 6.841 6.992 7.127 7.250 7.362 7.464 7.560 7.648 7.731 7.809 7.883 7.952 8.491 8.864 9.377 9.732 10.00
12 4.320 5.046 5.502 5.836 6.101 6.320 6.507 6.670 6.814 6.943 7.060 7.166 7.265 7.356 7.441 7.520 7.594 7.664 7.730 8.246 8.602 9.093 9.433 9.693
13 4.260 4.964 5.404 5.726 5.981 6.192 6.372 6.528 6.666 6.791 6.903 7.006 7.100 7.188 7.269 7.345 7.417 7.484 7.548 8.043 8.386 8.859 9.186 9.436
14 4.210 4.895 5.322 5.634 5.881 6.085 6.258 6.409 6.543 6.663 6.772 6.871 6.962 7.047 7.125 7.199 7.268 7.333 7.394 7.873 8.204 8.661 8.978 9.219
15 4.167 4.836 5.252 5.556 5.796 5.994 6.162 6.309 6.438 6.555 6.660 6.756 6.845 6.927 7.003 7.074 7.141 7.204 7.264 7.727 8.049 8.492 8.800 9.034
16 4.131 4.786 5.192 5.489 5.722 5.915 6.079 6.222 6.348 6.461 6.564 6.658 6.744 6.823 6.897 6.967 7.032 7.093 7.151 7.602 7.915 8.346 8.646 8.874
17 4.099 4.742 5.140 5.430 5.659 5.847 6.007 6.147 6.270 6.380 6.480 6.572 6.656 6.733 6.806 6.873 6.937 6.997 7.053 7.493 7.798 8.219 8.511 8.734
18 4.071 4.703 5.094 5.379 5.603 5.787 5.944 6.081 6.201 6.309 6.407 6.496 6.579 6.655 6.725 6.791 6.854 6.912 6.967 7.397 7.696 8.107 8.393 8.611
19 4.046 4.669 5.054 5.334 5.553 5.735 5.889 6.022 6.141 6.246 6.342 6.430 6.510 6.585 6.654 6.719 6.780 6.837 6.891 7.312 7.605 8.008 8.288 8.501
20 4.024 4.639 5.018 5.293 5.510 5.688 5.839 5.970 6.086 6.190 6.285 6.370 6.449 6.523 6.591 6.654 6.714 6.770 6.823 7.237 7.523 7.919 8.194 8.404
24 3.955 4.546 4.907 5.168 5.373 5.542 5.685 5.809 5.919 6.017 6.105 6.186 6.261 6.330 6.394 6.453 6.510 6.562 6.612 7.001 7.270 7.641 7.900 8.097
30 3.889 4.455 4.799 5.048 5.242 5.401 5.536 5.653 5.756 5.848 5.932 6.008 6.078 6.142 6.202 6.258 6.311 6.361 6.407 6.771 7.023 7.370 7.611 7.796
40 3.825 4.367 4.695 4.931 5.114 5.265 5.392 5.502 5.599 5.685 5.764 5.835 5.900 5.961 6.017 6.069 6.118 6.165 6.208 6.547 6.781 7.104 7.328 7.499
60 3.762 4.282 4.594 4.818 4.991 5.133 5.253 5.356 5.447 5.528 5.601 5.667 5.728 5.784 5.837 5.886 5.931 5.974 6.015 6.329 6.546 6.843 7.049 7.207
120 3.702 4.200 4.497 4.708 4.872 5.005 5.118 5.214 5.299 5.375 5.443 5.505 5.561 5.614 5.662 5.708 5.750 5.790 5.827 6.117 6.316 6.588 6.776 6.919
1e38 3.643 4.120 4.403 4.603 4.757 4.882 4.987 5.078 5.157 5.227 5.290 5.348 5.400 5.448 5.493 5.535 5.574 5.611 5.645 5.911 6.092 6.338 6.507 6.636"""

q0995 = """\
1 180.1 270.1 328.5 371.2 404.4 431.6 454.4 474.0 491.1 506.3 520.0 532.4 543.6 554.0 563.6 572.5 580.9 588.7 596.0 652.0 689.6 740.2 774.5 800.3
2 19.92 26.97 31.60 35.02 37.73 39.95 41.83 43.46 44.89 46.16 47.31 48.35 49.30 50.17 50.99 51.74 52.45 53.12 53.74 58.52 61.76 66.13 69.10 71.35
3 10.54 13.51 15.45 16.91 18.06 19.01 19.83 20.53 21.15 21.70 22.20 22.66 23.08 23.46 23.82 24.15 24.46 24.76 25.03 27.15 28.60 30.55 31.88 32.90
4 7.916 9.813 11.06 11.99 12.74 13.35 13.88 14.33 14.74 15.10 15.42 15.72 15.99 16.24 16.47 16.70 16.90 17.09 17.28 18.68 19.63 20.93 21.83 22.50
5 6.751 8.195 9.140 9.846 10.41 10.88 11.28 11.62 11.93 12.21 12.46 12.69 12.90 13.09 13.27 13.44 13.60 13.74 13.89 14.96 15.71 16.72 17.41 17.94
6 6.105 7.306 8.087 8.670 9.135 9.522 9.852 10.14 10.39 10.62 10.83 11.02 11.19 11.35 11.50 11.64 11.78 11.90 12.02 12.92 13.54 14.40 14.98 15.43
7 5.698 6.750 7.429 7.935 8.339 8.674 8.961 9.211 9.433 9.632 9.812 9.976 10.13 10.27 10.40 10.52 10.64 10.74 10.84 11.64 12.18 12.93 13.45 13.85
8 5.420 6.370 6.981 7.435 7.796 8.097 8.354 8.578 8.777 8.955 9.117 9.265 9.401 9.527 9.644 9.754 9.856 9.953 10.04 10.76 11.25 11.92 12.39 12.75
9 5.218 6.096 6.657 7.073 7.405 7.680 7.915 8.120 8.302 8.466 8.614 8.749 8.874 8.989 9.097 9.197 9.292 9.381 9.465 10.12 10.57 11.19 11.62 11.95
10 5.065 5.888 6.412 6.800 7.109 7.365 7.584 7.775 7.944 8.096 8.233 8.359 8.475 8.583 8.683 8.777 8.864 8.947 9.025 9.635 10.06 10.64 11.04 11.35
11 4.945 5.726 6.221 6.587 6.878 7.119 7.325 7.504 7.664 7.807 7.936 8.055 8.164 8.265 8.359 8.447 8.530 8.608 8.681 9.255 9.653 10.20 10.58 10.87
12 4.849 5.596 6.068 6.416 6.693 6.922 7.117 7.288 7.439 7.574 7.697 7.810 7.913 8.009 8.099 8.182 8.261 8.335 8.405 8.950 9.328 9.850 10.21 10.49
13 4.769 5.489 5.943 6.276 6.541 6.760 6.947 7.110 7.254 7.384 7.502 7.609 7.708 7.800 7.885 7.965 8.040 8.111 8.178 8.699 9.061 9.560 9.907 10.17
14 4.703 5.401 5.838 6.160 6.414 6.625 6.805 6.962 7.101 7.225 7.338 7.442 7.537 7.625 7.707 7.784 7.856 7.924 7.988 8.489 8.837 9.317 9.651 9.906
15 4.647 5.325 5.750 6.061 6.307 6.511 6.685 6.836 6.970 7.091 7.200 7.300 7.391 7.476 7.556 7.630 7.699 7.765 7.827 8.310 8.647 9.111 9.434 9.680
16 4.599 5.261 5.674 5.976 6.216 6.413 6.582 6.729 6.859 6.975 7.081 7.178 7.267 7.349 7.426 7.498 7.565 7.629 7.689 8.157 8.483 8.933 9.246 9.486
17 4.557 5.205 5.608 5.903 6.136 6.329 6.493 6.636 6.762 6.876 6.978 7.072 7.159 7.239 7.313 7.383 7.449 7.510 7.569 8.024 8.341 8.779 9.083 9.316
18 4.521 5.156 5.550 5.839 6.067 6.255 6.415 6.554 6.677 6.788 6.888 6.980 7.064 7.142 7.215 7.283 7.347 7.407 7.464 7.908 8.216 8.643 8.940 9.167
19 4.488 5.112 5.500 5.782 6.005 6.189 6.346 6.482 6.603 6.711 6.809 6.898 6.981 7.057 7.128 7.194 7.257 7.316 7.371 7.805 8.106 8.523 8.813 9.035
20 4.460 5.074 5.455 5.732 5.951 6.131 6.285 6.418 6.536 6.642 6.738 6.826 6.906 6.981 7.051 7.116 7.177 7.234 7.289 7.713 8.008 8.416 8.700 8.917
24 4.371 4.955 5.315 5.577 5.783 5.952 6.096 6.221 6.331 6.430 6.520 6.602 6.677 6.747 6.812 6.872 6.929 6.983 7.034 7.429 7.703 8.083 8.348 8.551
30 4.285 4.841 5.181 5.428 5.621 5.779 5.914 6.031 6.134 6.226 6.310 6.386 6.456 6.521 6.581 6.638 6.691 6.740 6.787 7.154 7.409 7.760 8.005 8.193
40 4.202 4.731 5.052 5.284 5.465 5.614 5.739 5.848 5.944 6.030 6.108 6.178 6.243 6.303 6.359 6.411 6.460 6.507 6.550 6.888 7.123 7.447 7.672 7.844
60 4.122 4.625 4.928 5.146 5.316 5.454 5.571 5.673 5.762 5.841 5.913 5.979 6.039 6.094 6.146 6.194 6.239 6.281 6.321 6.632 6.846 7.142 7.347 7.504
120 4.044 4.523 4.809 5.013 5.172 5.301 5.410 5.504 5.586 5.660 5.726 5.786  5.842 5.893 5.940 5.984 6.025 6.064 6.101 6.384 6.579 6.846 7.031 7.172
1e38 3.970 4.424 4.694 4.886 5.033 5.154 5.255 5.341 5.418 5.485 5.546 5.602  5.652 5.699 5.742 5.783 5.820 5.856 5.889 6.146 6.322 6.561 6.725 6.850"""

q0999 = """\
1 900.3 1351. 1643. 1856. 2022. 2158. 2272. 2370. 2455. 2532. 2600. 2662. 2718. 2770. 2818. 2863. 2904. 2943. 2980. 3260. 3448. 3701. 3873. 4002.
2 44.69 60.42 70.77 78.43 84.49 89.46 93.67 97.30 100.5 103.3 105.9 108.2 110.4 112.3 114.2 115.9 117.4 118.9 120.3 131.0 138.3 148.0 154.7 159.7
3 18.28 23.32 26.65 29.13 31.11 32.74 34.12 35.33 36.39 37.34 38.20 38.98 39.69 40.35 40.97 41.54 42.07 42.58 43.05 46.68 49.16 52.51 54.81 56.53
4 12.18 14.98 16.84 18.23 19.34 20.26 21.04 21.73 22.33 22.87 23.36 23.81 24.21 24.59 24.94 25.27 25.58 25.87 26.14 28.24 29.68 31.65 32.98 34.00
5 9.714 11.67 12.96 13.93 14.71 15.35 15.91 16.39 16.82 17.18 17.53 17.85 18.13 18.41 18.66 18.89 19.10 19.31 19.51 21.01 22.03 23.45 24.41 25.15
6 8.427 9.960 10.97 11.72 12.32 12.82 13.25 13.63 13.96 14.26 14.53 14.78 15.00 15.21 15.41 15.59 15.78 15.94 16.09 17.28 18.10 19.22 20.00 20.58
7 7.648 8.930 9.768 10.40 10.90 11.32 11.67 11.99 12.27 12.52 12.74 12.95 13.14 13.32 13.48 13.64 13.78 13.92 14.05 15.06 15.74 16.69 17.35 17.85
8 7.129 8.250 8.977 9.522 9.958 10.32 10.63 10.90 11.15 11.36 11.56 11.74 11.91 12.06 12.20 12.34 12.46 12.58 12.69 13.57 14.17 15.01 15.59 16.02
9 6.761 7.768 8.419 8.906 9.295 9.619 9.896 10.14 10.35 10.55 10.72 10.89 11.03 11.17 11.30 11.42 11.53 11.64 11.74 12.52 13.07 13.82 14.34 14.74
10 6.487 7.411 8.006 8.449 8.804 9.099 9.352 9.573 9.769 9.946 10.11 10.25 10.39 10.51 10.63 10.74 10.84 10.94 11.03 11.75 12.25 12.94 13.42 13.79
11 6.275 7.135 7.687 8.098 8.426 8.699 8.933 9.137 9.319 9.482 9.630 9.766 9.891 10.01 10.12 10.22 10.31 10.40 10.49 11.15 11.61 12.25 12.70 13.03
12 6.106 6.917 7.435 7.820 8.127 8.382 8.601 8.792 8.962 9.115 9.253 9.380 9.497 9.606 9.707 9.802 9.891 9.975 10.05 10.68 11.11 11.71 12.12 12.44
13 5.969 6.740 7.231 7.595 7.885 8.126 8.332 8.513 8.673 8.817 8.948 9.068 9.178 9.280 9.376 9.465 9.549 9.629 9.704 10.29 10.70 11.27 11.66 11.96
14 5.855 6.593 7.062 7.409 7.685 7.914 8.110 8.282 8.434 8.571 8.695 8.809 8.914 9.011 9.102 9.187 9.267 9.342 9.414 9.972 10.36 10.90 11.28 11.57
15 5.760 6.470 6.920 7.252 7.517 7.736 7.924 8.088 8.234 8.364 8.483 8.592 8.692 8.785 8.872 8.953 9.030 9.102 9.170 9.703 10.08 10.59 10.95 11.23
16 5.678 6.365 6.799 7.119 7.374 7.585 7.765 7.923 8.063 8.189 8.303 8.407 8.504 8.593 8.676 8.754 8.828 8.897 8.962 9.475 9.832 10.33 10.68 10.94
17 5.608 6.274 6.695 7.004 7.250 7.454 7.629 7.781 7.916 8.037 8.147 8.248 8.341 8.427 8.507 8.583 8.653 8.720 8.783 9.277 9.623 10.10 10.44 10.69
18 5.546 6.195 6.604 6.905 7.143 7.341 7.510 7.657 7.788 7.905 8.012 8.109 8.199 8.283 8.361 8.433 8.502 8.566 8.627 9.106 9.440 9.904 10.23 10.48
19 5.492 6.126 6.524 6.817 7.049 7.241 7.405 7.549 7.676 7.790 7.893 7.988 8.075 8.156 8.232 8.302 8.369 8.431 8.491 8.955 9.279 9.729 10.04 10.29
20 5.444 6.065 6.454 6.740 6.966 7.153 7.313 7.453 7.576 7.687 7.788 7.880 7.965 8.044 8.118 8.186 8.251 8.312 8.370 8.821 9.136 9.575 9.881 10.12
24 5.297 5.877 6.238 6.502 6.711 6.884 7.031 7.159 7.272 7.374 7.467 7.551 7.629 7.701 7.768 7.831 7.890 7.946 7.999 8.411 8.699 9.100 9.380 9.595
30 5.156 5.698 6.033 6.277 6.469 6.628 6.763 6.880 6.984 7.077 7.161 7.239 7.310 7.375 7.437 7.494 7.548 7.598 7.646 8.021 8.283 8.646 8.901 9.096
40 5.022 5.527 5.838 6.063 6.240 6.385 6.509 6.616 6.710 6.795 6.872 6.942 7.007 7.066 7.122 7.174 7.223 7.268 7.312 7.651 7.887 8.214 8.442 8.618
60 4.893 5.365 5.653 5.860 6.022 6.155 6.268 6.365 6.451 6.528 6.598 6.661 6.720 6.773 6.824 6.870 6.914 6.956 6.995 7.299 7.510 7.802 8.005 8.161
120 4.771 5.211 5.476 5.667 5.815 5.937 6.039 6.128 6.206 6.275 6.338 6.395 6.448 6.496 6.541 6.583 6.623 6.660 6.695 6.966 7.153 7.410 7.589 7.726
1e38 4.654 5.063 5.309 5.484 5.619 5.730 5.823 5.903 5.973 6.036 6.092 6.144 6.191 6.234 6.274 6.312 6.347 6.380 6.411 6.651 6.816 7.041 7.196 7.314"""

# Build the T+ 'matrix'
# T is a dict of dicts of lists

#                 [alpha keys]        [v keys]
#                   [table values as lists of floats]
T = dict([(0.100, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0100.split('\n')}),
          (0.500, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0500.split('\n')}),
          (0.675, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0675.split('\n')}),
          (0.750, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0750.split('\n')}),
          (0.800, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0800.split('\n')}),
          (0.850, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0850.split('\n')}),
          (0.900, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0900.split('\n')}),
          (0.950, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0950.split('\n')}),
          (0.975, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0975.split('\n')}),
          (0.990, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0990.split('\n')}),
          (0.995, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0995.split('\n')}),
          (0.999, {float(L.split()[0]):
                         lmap(float, L.split()[1:]) for L in q0999.split('\n')})])

# This dict maps r values to the correct list index
R = dict(zip([2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,
                     17,18,19,20,30,40,60,80,100], lrange(24)))

inf = np.inf
# we will need a tinv function
_tinv = scipy.stats.t.isf
_phi = scipy.stats.norm.isf

# Now we can build the A 'matrix'

# these are for the least squares fitting
def qhat(a, p, r, v):

    # eq. 2.3
    p_ = (1. + p) /2.

    f = a[0]*np.log(r-1.) + \
        a[1]*np.log(r-1.)**2 + \
        a[2]*np.log(r-1.)**3 + \
        a[3]*np.log(r-1.)**4

    # eq. 2.7 and 2.8 corrections
    for i, r_ in enumerate(r):
        if r_ == 3:
            f[i] += -0.002 / (1. + 12. * _phi(p)**2)

            if v <= 4.364:
                f[i] += 1./517. - 1./(312.*v)
            else:
                f[i] += 1./(191.*v)

    return math.sqrt(2) * (f - 1.) * _tinv(p_, v)

errfunc = lambda a, p, r, v, q: qhat(a, p, r, v) - q

A = {} # this is the error matrix
for p in T:
    for v in T[p]:
        #eq. 2.4
        a0 = random(4)
        a1, success = leastsq(errfunc, a0,
                              args=(p, np.array(list(R.keys())),
                                    v, np.array(T[p][v])))

        if v == 1e38:
            A[(p,inf)] = list(a1)
        else:
            A[(p,v)] = list(a1)

raise ImportError("we do not want to import this")
# uncomment the lines below to repr-ize A
##import pprint
##pprint.pprint(A, width=160)
