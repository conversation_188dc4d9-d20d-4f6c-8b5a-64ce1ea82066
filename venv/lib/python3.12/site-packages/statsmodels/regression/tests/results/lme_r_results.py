import numpy as np

coef_ml_drf_0 = np.array([-0.9887517])

vcov_ml_drf_0 = np.array([0.001317148]).reshape(1, 1, order='F')

cov_re_ml_drf_0 = np.array([0.2522485]).reshape(1, 1, order='F')

scale_ml_drf_0 = np.array([0.2718486])

loglike_ml_drf_0 = np.array([-240.1254])

ranef_mean_ml_drf_0 = np.array([0.04977167])

ranef_condvar_ml_drf_0 = np.array([0.130841])

coef_reml_drf_0 = np.array([-0.9887533])

vcov_reml_drf_0 = np.array([0.001323559]).reshape(1, 1, order='F')

cov_re_reml_drf_0 = np.array([0.2524129]).reshape(1, 1, order='F')

scale_reml_drf_0 = np.array([0.2733467])

loglike_reml_drf_0 = np.array([-242.5214])

ranef_mean_reml_drf_0 = np.array([0.04964696])

ranef_condvar_reml_drf_0 = np.array([0.1312315])

coef_ml_drf_1 = np.array([-0.9115929])

vcov_ml_drf_1 = np.array([0.01340632]).reshape(1, 1, order='F')

cov_re_ml_drf_1 = np.array([0]).reshape(1, 1, order='F')

scale_ml_drf_1 = np.array([4.050921])

loglike_ml_drf_1 = np.array([-538.0763])

ranef_mean_ml_drf_1 = np.array([0])

ranef_condvar_ml_drf_1 = np.array([0])

coef_reml_drf_1 = np.array([-0.9115929])

vcov_reml_drf_1 = np.array([0.01345931]).reshape(1, 1, order='F')

cov_re_reml_drf_1 = np.array([2.839777e-14]).reshape(1, 1, order='F')

scale_reml_drf_1 = np.array([4.066932])

loglike_reml_drf_1 = np.array([-539.3124])

ranef_mean_reml_drf_1 = np.array([2.424384e-14])

ranef_condvar_reml_drf_1 = np.array([2.839777e-14])

coef_ml_drf_2 = np.array([-1.012044, 0.9789052])

vcov_ml_drf_2 = np.array([
    0.00117849, 1.458744e-05,
    1.458744e-05, 0.001054926]).reshape(2, 2, order='F')

cov_re_ml_drf_2 = np.array([0.1596058]).reshape(1, 1, order='F')

scale_ml_drf_2 = np.array([0.2129146])

loglike_ml_drf_2 = np.array([-200.319])

ranef_mean_ml_drf_2 = np.array([0.3197174])

ranef_condvar_ml_drf_2 = np.array([0.09122291])

coef_reml_drf_2 = np.array([-1.012137, 0.9790792])

vcov_reml_drf_2 = np.array([
    0.001190455, 1.482666e-05,
    1.482666e-05, 0.001066002]).reshape(2, 2, order='F')

cov_re_reml_drf_2 = np.array([0.1595015]).reshape(1, 1, order='F')

scale_reml_drf_2 = np.array([0.2154276])

loglike_reml_drf_2 = np.array([-205.275])

ranef_mean_reml_drf_2 = np.array([0.3172978])

ranef_condvar_reml_drf_2 = np.array([0.09164674])

coef_ml_drf_3 = np.array([-1.028053, 0.8602685])

vcov_ml_drf_3 = np.array([
    0.01398831, 0.001592619, 0.001592619, 0.01602274]).reshape(2, 2, order='F')

cov_re_ml_drf_3 = np.array([0.8130996]).reshape(1, 1, order='F')

scale_ml_drf_3 = np.array([3.100447])

loglike_ml_drf_3 = np.array([-477.1707])

ranef_mean_ml_drf_3 = np.array([-0.2641747])

ranef_condvar_ml_drf_3 = np.array([0.6441656])

coef_reml_drf_3 = np.array([-1.027583, 0.8605714])

vcov_reml_drf_3 = np.array([
    0.01411922, 0.001607343, 0.001607343, 0.01617574]).reshape(2, 2, order='F')

cov_re_reml_drf_3 = np.array([0.8117898]).reshape(1, 1, order='F')

scale_reml_drf_3 = np.array([3.13369])

loglike_reml_drf_3 = np.array([-479.5354])

ranef_mean_reml_drf_3 = np.array([-0.2614875])

ranef_condvar_reml_drf_3 = np.array([0.6447625])

coef_ml_drf_4 = np.array([-1.005151, -0.003657404, 1.054786])

vcov_ml_drf_4 = np.array([
    0.001190639, -5.327162e-05, 5.992985e-05, -5.327162e-05,
    0.001460303, -2.662532e-05, 5.992985e-05, -2.662532e-05,
    0.00148609]).reshape(3, 3, order='F')

cov_re_ml_drf_4 = np.array([0.1703249]).reshape(1, 1, order='F')

scale_ml_drf_4 = np.array([0.251763])

loglike_ml_drf_4 = np.array([-231.6389])

ranef_mean_ml_drf_4 = np.array([-0.2063164])

ranef_condvar_ml_drf_4 = np.array([0.0459578])

coef_reml_drf_4 = np.array([-1.005067, -0.003496032, 1.054666])

vcov_reml_drf_4 = np.array([
    0.001206925, -5.4182e-05, 6.073475e-05, -5.4182e-05,
    0.001479871, -2.723494e-05, 6.073475e-05, -2.723494e-05,
    0.001506198]).reshape(3, 3, order='F')

cov_re_reml_drf_4 = np.array([0.1705659]).reshape(1, 1, order='F')

scale_reml_drf_4 = np.array([0.2556394])

loglike_reml_drf_4 = np.array([-238.761])

ranef_mean_reml_drf_4 = np.array([-0.2055303])

ranef_condvar_reml_drf_4 = np.array([0.04649027])

coef_ml_drf_5 = np.array([-0.8949725, 0.08141558, 1.052529])

vcov_ml_drf_5 = np.array([
    0.01677563, 0.0008077524, -0.001255011, 0.0008077524,
    0.01719346, 0.0009266736, -0.001255011, 0.0009266736,
    0.01608435]).reshape(3, 3, order='F')

cov_re_ml_drf_5 = np.array([0.3444677]).reshape(1, 1, order='F')

scale_ml_drf_5 = np.array([4.103944])

loglike_ml_drf_5 = np.array([-579.4568])

ranef_mean_ml_drf_5 = np.array([0.08254713])

ranef_condvar_ml_drf_5 = np.array([0.3177935])

coef_reml_drf_5 = np.array([-0.8946164, 0.08134261, 1.052486])

vcov_reml_drf_5 = np.array([
    0.0169698, 0.0008162714, -0.001268635, 0.0008162714,
    0.01739219, 0.0009345538, -0.001268635, 0.0009345538,
    0.01627074]).reshape(3, 3, order='F')

cov_re_reml_drf_5 = np.array([0.3420993]).reshape(1, 1, order='F')

scale_reml_drf_5 = np.array([4.155737])

loglike_reml_drf_5 = np.array([-582.8377])

ranef_mean_reml_drf_5 = np.array([0.08111449])

ranef_condvar_reml_drf_5 = np.array([0.3160797])

coef_ml_drf_6 = np.array([-0.8885425])

vcov_ml_drf_6 = np.array([0.002443738]).reshape(1, 1, order='F')

cov_re_ml_drf_6 = np.array([
    0.2595201, 0.04591071,
    0.04591071, 2.204612]).reshape(2, 2, order='F')

scale_ml_drf_6 = np.array([0.243133])

loglike_ml_drf_6 = np.array([-382.551])

ranef_mean_ml_drf_6 = np.array([-0.0597406, 0.6037288])

ranef_condvar_ml_drf_6 = np.array([
    0.2420741, 0.2222169,
    0.2222169, 0.4228908]).reshape(2, 2, order='F')

coef_reml_drf_6 = np.array([-0.8883881])

vcov_reml_drf_6 = np.array([0.002461581]).reshape(1, 1, order='F')

cov_re_reml_drf_6 = np.array([
    0.2595767, 0.04590012,
    0.04590012, 2.204822]).reshape(2, 2, order='F')

scale_reml_drf_6 = np.array([0.2453537])

loglike_reml_drf_6 = np.array([-384.6373])

ranef_mean_reml_drf_6 = np.array([-0.05969892, 0.6031793])

ranef_condvar_reml_drf_6 = np.array([
    0.2421365, 0.2221108,
    0.2221108, 0.4244443]).reshape(2, 2, order='F')

coef_ml_irf_6 = np.array([-0.8874992])

vcov_ml_irf_6 = np.array([0.002445505]).reshape(1, 1, order='F')

cov_re_ml_irf_6 = np.array([
    0.2587624, 0,
    0, 2.188653]).reshape(2, 2, order='F')

scale_ml_irf_6 = np.array([0.2432694])

loglike_ml_irf_6 = np.array([-382.6581])

coef_reml_irf_6 = np.array([-0.8873394])

vcov_reml_irf_6 = np.array([0.002463375]).reshape(1, 1, order='F')

cov_re_reml_irf_6 = np.array([
    0.2588157, 0,
    0, 2.188876]).reshape(2, 2, order='F')

scale_reml_irf_6 = np.array([0.2454935])

loglike_reml_irf_6 = np.array([-384.7441])

coef_ml_drf_7 = np.array([-0.9645281])

vcov_ml_drf_7 = np.array([0.01994]).reshape(1, 1, order='F')

cov_re_ml_drf_7 = np.array([
    0.2051329, 0.0734377,
    0.0734377, 3.285381]).reshape(2, 2, order='F')

scale_ml_drf_7 = np.array([3.423247])

loglike_ml_drf_7 = np.array([-587.7101])

ranef_mean_ml_drf_7 = np.array([0.07007965, -0.2920284])

ranef_condvar_ml_drf_7 = np.array([
    0.1823183, 0.02247519,
    0.02247519, 1.125011]).reshape(2, 2, order='F')

coef_reml_drf_7 = np.array([-0.9647862])

vcov_reml_drf_7 = np.array([0.02002546]).reshape(1, 1, order='F')

cov_re_reml_drf_7 = np.array([
    0.2056226, 0.0726139,
    0.0726139, 3.2876]).reshape(2, 2, order='F')

scale_reml_drf_7 = np.array([3.440244])

loglike_reml_drf_7 = np.array([-588.7476])

ranef_mean_reml_drf_7 = np.array([0.07000628, -0.2916737])

ranef_condvar_reml_drf_7 = np.array([
    0.1828266, 0.02229138,
    0.02229138, 1.128947]).reshape(2, 2, order='F')

coef_ml_irf_7 = np.array([-0.9665524])

vcov_ml_irf_7 = np.array([0.01998144]).reshape(1, 1, order='F')

cov_re_ml_irf_7 = np.array([
    0.2021561, 0, 0, 3.270735]).reshape(2, 2, order='F')

scale_ml_irf_7 = np.array([3.423186])

loglike_ml_irf_7 = np.array([-587.7456])

coef_reml_irf_7 = np.array([-0.9667854])

vcov_reml_irf_7 = np.array([0.02006657]).reshape(1, 1, order='F')

cov_re_reml_irf_7 = np.array([
    0.2026938, 0, 0, 3.273129]).reshape(2, 2, order='F')

scale_reml_irf_7 = np.array([3.440197])

loglike_reml_irf_7 = np.array([-588.782])

coef_ml_drf_8 = np.array([-1.083882, 0.8955623])

vcov_ml_drf_8 = np.array([
    0.002491643, 0.0001693531,
    0.0001693531, 0.00253309]).reshape(2, 2, order='F')

cov_re_ml_drf_8 = np.array([
    0.1506188, 0.126091, 0.126091, 2.485462]).reshape(2, 2, order='F')

scale_ml_drf_8 = np.array([0.2586519])

loglike_ml_drf_8 = np.array([-363.6234])

ranef_mean_ml_drf_8 = np.array([0.2852326, -0.5047804])

ranef_condvar_ml_drf_8 = np.array([
    0.05400391, 0.002330104,
    0.002330104, 0.122761]).reshape(2, 2, order='F')

coef_reml_drf_8 = np.array([-1.083938, 0.8956893])

vcov_reml_drf_8 = np.array([
    0.002528969, 0.0001712206,
    0.0001712206, 0.002573335]).reshape(2, 2, order='F')

cov_re_reml_drf_8 = np.array([
    0.1505098, 0.1256311,
    0.1256311, 2.484219]).reshape(2, 2, order='F')

scale_reml_drf_8 = np.array([0.2635901])

loglike_reml_drf_8 = np.array([-367.7667])

ranef_mean_reml_drf_8 = np.array([0.2829798, -0.5042857])

ranef_condvar_reml_drf_8 = np.array([
    0.05463632, 0.002393538,
    0.002393538, 0.1249828]).reshape(2, 2, order='F')

coef_ml_irf_8 = np.array([-1.079481, 0.898216])

vcov_ml_irf_8 = np.array([
    0.002511536, 0.0001812511,
    0.0001812511, 0.002573405]).reshape(2, 2, order='F')

cov_re_ml_irf_8 = np.array([
    0.1498568, 0, 0, 2.403849]).reshape(2, 2, order='F')

scale_ml_irf_8 = np.array([0.2605245])

loglike_ml_irf_8 = np.array([-364.4824])

coef_reml_irf_8 = np.array([-1.07952, 0.8983678])

vcov_reml_irf_8 = np.array([
    0.002549354, 0.0001833386,
    0.0001833386, 0.002614672]).reshape(2, 2, order='F')

cov_re_reml_irf_8 = np.array([
    0.1497193, 0, 0, 2.403076]).reshape(2, 2, order='F')

scale_reml_irf_8 = np.array([0.2655558])

loglike_reml_irf_8 = np.array([-368.6141])

coef_ml_drf_9 = np.array([-1.272698, 0.8617471])

vcov_ml_drf_9 = np.array([
    0.02208544, 0.001527479, 0.001527479, 0.02597528]).reshape(2, 2, order='F')

cov_re_ml_drf_9 = np.array([
    0.510175, 0.08826114, 0.08826114, 3.342888]).reshape(2, 2, order='F')

scale_ml_drf_9 = np.array([3.722112])

loglike_ml_drf_9 = np.array([-589.8274])

ranef_mean_ml_drf_9 = np.array([0.03253644, 0.224043])

ranef_condvar_ml_drf_9 = np.array([
    0.3994872, 0.02478884, 0.02478884, 1.195077]).reshape(2, 2, order='F')

coef_reml_drf_9 = np.array([-1.272483, 0.861814])

vcov_reml_drf_9 = np.array([
    0.02228589, 0.001535598, 0.001535598, 0.0262125]).reshape(2, 2, order='F')

cov_re_reml_drf_9 = np.array([
    0.5123204, 0.08897376, 0.08897376, 3.341722]).reshape(2, 2, order='F')

scale_reml_drf_9 = np.array([3.764058])

loglike_reml_drf_9 = np.array([-591.7188])

ranef_mean_reml_drf_9 = np.array([0.03239688, 0.2230525])

ranef_condvar_reml_drf_9 = np.array([
    0.401762, 0.02521271, 0.02521271, 1.203536]).reshape(2, 2, order='F')

coef_ml_irf_9 = np.array([-1.277018, 0.86395])

vcov_ml_irf_9 = np.array([
    0.02205706, 0.001509887, 0.001509887, 0.02599941]).reshape(2, 2, order='F')

cov_re_ml_irf_9 = np.array([
    0.5086816, 0, 0, 3.312757]).reshape(2, 2, order='F')

scale_ml_irf_9 = np.array([3.72105])

loglike_ml_irf_9 = np.array([-589.8628])

coef_reml_irf_9 = np.array([-1.276822, 0.8640243])

vcov_reml_irf_9 = np.array([
    0.02225705, 0.001517774, 0.001517774, 0.02623682]).reshape(2, 2, order='F')

cov_re_reml_irf_9 = np.array([
    0.5107725, 0, 0, 3.31152]).reshape(2, 2, order='F')

scale_reml_irf_9 = np.array([3.762967])

loglike_reml_irf_9 = np.array([-591.7543])

coef_ml_drf_10 = np.array([-0.9419566, -0.02359824, 1.085796])

vcov_ml_drf_10 = np.array([
    0.001963536, -0.0003221793, 0.0001950186, -0.0003221793,
    0.002534251, 0.0004107718, 0.0001950186, 0.0004107718,
    0.002580736]).reshape(3, 3, order='F')

cov_re_ml_drf_10 = np.array([
    0.2040541, 0.09038325, 0.09038325, 2.218903]).reshape(2, 2, order='F')

scale_ml_drf_10 = np.array([0.2558286])

loglike_ml_drf_10 = np.array([-379.6591])

ranef_mean_ml_drf_10 = np.array([0.03876325, -0.725853])

ranef_condvar_ml_drf_10 = np.array([
    0.1988816, 0.1872403, 0.1872403, 0.4052274]).reshape(2, 2, order='F')

coef_reml_drf_10 = np.array([-0.9426367, -0.02336203, 1.085733])

vcov_reml_drf_10 = np.array([
    0.002011348, -0.0003300612, 0.0002002948, -0.0003300612,
    0.002589149, 0.000418987, 0.0002002948, 0.000418987,
    0.002637433]).reshape(3, 3, order='F')

cov_re_reml_drf_10 = np.array([
    0.2034827, 0.09063836, 0.09063836, 2.219191]).reshape(2, 2, order='F')

scale_reml_drf_10 = np.array([0.2630213])

loglike_reml_drf_10 = np.array([-386.0008])

ranef_mean_reml_drf_10 = np.array([0.03838686, -0.7240812])

ranef_condvar_reml_drf_10 = np.array([
    0.1983981, 0.1865469, 0.1865469, 0.4100937]).reshape(2, 2, order='F')

coef_ml_irf_10 = np.array([-0.9441033, -0.01755913, 1.088568])

vcov_ml_irf_10 = np.array([
    0.001960114, -0.0003215658, 0.0001944005, -0.0003215658,
    0.00253441, 0.0004061179, 0.0001944005, 0.0004061179,
    0.002589158]).reshape(3, 3, order='F')

cov_re_ml_irf_10 = np.array([
    0.2032228, 0, 0, 2.192893]).reshape(2, 2, order='F')

scale_ml_irf_10 = np.array([0.2553399])

loglike_ml_irf_10 = np.array([-380.162])

coef_reml_irf_10 = np.array([-0.9448257, -0.01722993, 1.088557])

vcov_reml_irf_10 = np.array([
    0.00200783, -0.0003294349, 0.0001996613, -0.0003294349,
    0.00258937, 0.0004141667, 0.0001996613, 0.0004141667,
    0.002646242]).reshape(3, 3, order='F')

cov_re_reml_irf_10 = np.array([
    0.2026653, 0, 0, 2.193124]).reshape(2, 2, order='F')

scale_reml_irf_10 = np.array([0.2625147])

loglike_reml_irf_10 = np.array([-386.5024])

coef_ml_drf_11 = np.array([-1.36971, 0.1596278, 0.8588724])

vcov_ml_drf_11 = np.array([
    0.0232326, 0.00172214, 0.002275343, 0.00172214,
    0.02318941, 0.0004755663, 0.002275343, 0.0004755663,
    0.02123474]).reshape(3, 3, order='F')

cov_re_ml_drf_11 = np.array([
    0.3719096, 0.332198, 0.332198, 1.120588]).reshape(2, 2, order='F')

scale_ml_drf_11 = np.array([4.849781])

loglike_ml_drf_11 = np.array([-601.6432])

ranef_mean_ml_drf_11 = np.array([-0.4256917, -0.3907759])

ranef_condvar_ml_drf_11 = np.array([
    0.2987928, 0.1992074, 0.1992074, 0.7477486]).reshape(2, 2, order='F')

coef_reml_drf_11 = np.array([-1.370236, 0.1597671, 0.8585994])

vcov_reml_drf_11 = np.array([
    0.02351795, 0.001749756, 0.002301599, 0.001749756,
    0.02346869, 0.0004785668, 0.002301599, 0.0004785668,
    0.02149093]).reshape(3, 3, order='F')

cov_re_reml_drf_11 = np.array([
    0.3680346, 0.3324419, 0.3324419, 1.118623]).reshape(2, 2, order='F')

scale_reml_drf_11 = np.array([4.922222])

loglike_reml_drf_11 = np.array([-604.5746])

ranef_mean_reml_drf_11 = np.array([-0.4168539, -0.3879533])

ranef_condvar_reml_drf_11 = np.array([
    0.2965372, 0.2010191, 0.2010191, 0.7503986]).reshape(2, 2, order='F')

coef_ml_irf_11 = np.array([-1.370117, 0.1414964, 0.8466083])

vcov_ml_irf_11 = np.array([
    0.02319951, 0.001705996, 0.002265252, 0.001705996,
    0.02345623, 0.000514879, 0.002265252, 0.000514879,
    0.02153162]).reshape(3, 3, order='F')

cov_re_ml_irf_11 = np.array([
    0.4004789, 0, 0, 1.108087]).reshape(2, 2, order='F')

scale_ml_irf_11 = np.array([4.78776])

loglike_ml_irf_11 = np.array([-602.308])

coef_reml_irf_11 = np.array([-1.370663, 0.1417561, 0.8464232])

vcov_reml_irf_11 = np.array([
    0.02348548, 0.001734072, 0.002291519, 0.001734072,
    0.02373715, 0.0005177618, 0.002291519, 0.0005177618,
    0.02178966]).reshape(3, 3, order='F')

cov_re_reml_irf_11 = np.array([
    0.3966454, 0, 0, 1.106551]).reshape(2, 2, order='F')

scale_reml_irf_11 = np.array([4.860342])

loglike_reml_irf_11 = np.array([-605.2274])
